#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
快速验证脚本：一键验证metricsToKafka的消息写入情况
"""

import subprocess
import sys
import json
import socket
import time

def run_kafka_command(command):
    """运行kafka验证命令"""
    try:
        result = subprocess.run([sys.executable, "kafka_verification_tool.py"] + command.split(), 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(result.stdout)
            return True
        else:
            print(f"命令执行失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"执行异常: {e}")
        return False

def send_test_message_to_metrics():
    """向metricsToKafka发送测试消息"""
    print("📤 向metricsToKafka发送测试消息...")
    
    try:
        # 连接到metricsToKafka服务
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('192.168.161.27', 8686))
        
        # 发送测试消息
        test_message = {
            "model": "chfp",
            "body": f"verification_test_{int(time.time())}"
        }
        
        sock.send(json.dumps(test_message).encode('utf-8'))
        print(f"✅ 测试消息已发送: {test_message}")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ 发送失败: {e}")
        return False

def main():
    """主要验证流程"""
    print("🔍 开始快速验证metricsToKafka消息写入情况")
    print("=" * 60)
    
    # 1. 检查Topic列表
    print("\n📋 步骤1: 检查可用Topic")
    if not run_kafka_command("list"):
        print("❌ 无法连接到Kafka，请检查配置")
        return
    
    # 2. 检查目标Topic信息
    print("\n📊 步骤2: 检查目标Topic信息")
    topics = ["chfp_topic", "hostAlive_topic-1", "hostAlive_topic-2"]
    
    for topic in topics:
        print(f"\n检查Topic: {topic}")
        run_kafka_command(f"info {topic}")
    
    # 3. 发送测试消息到metricsToKafka
    print("\n📤 步骤3: 发送测试消息")
    if not send_test_message_to_metrics():
        print("❌ 无法发送测试消息到metricsToKafka服务")
        return
    
    # 等待消息处理
    print("⏳ 等待3秒让消息处理完成...")
    time.sleep(3)
    
    # 4. 验证消息是否写入Kafka
    print("\n🔍 步骤4: 验证消息是否写入chfp_topic")
    run_kafka_command("consume chfp_topic --max 5")
    
    print("\n✅ 验证完成!")
    print("\n💡 提示:")
    print("  - 如果看到新的消息，说明metricsToKafka工作正常")
    print("  - 如果没有新消息，检查metricsToKafka日志和配置")
    print("  - 使用 'python kafka_verification_tool.py monitor chfp_topic' 实时监控")

if __name__ == "__main__":
    main()