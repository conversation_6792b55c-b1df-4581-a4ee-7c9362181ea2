#!/usr/bin/python
# -*- coding: utf-8 -*-

import pymysql,psycopg2,cx_Oracle
import time
import traceback
import xml.etree.ElementTree as ET
from desTool import ASEUtil
import logging2

class DBPyMgr:
    def __init__(self, cfg_xml_file, db_name=None):
        self.cfg_xml_file = cfg_xml_file
        self.m_db_name = db_name
        self.m_db_info = {}
        self.m_sql_cluster_info = {}
        self.m_conn = {}


    def init(self):
        if self.init_config() == False:
            logging2.error("init config failed")
            return False
        if self.check_cfg_info() == False:
            logging2.error("check config info failed")
            return False
        if self.create_conn() == False:
            logging2.error("create conn failed")
            return False
        return True


    def init_config(self):
        try:
            # 解析XML文件
            tree = ET.parse(self.cfg_xml_file)
            root = tree.getroot()

            # 获取db_set下所有db的信息
            self.m_db_info = {}
            db_set = root.find('db_set')
            if db_set is not None:
                for db in db_set.findall('db'):
                    name = db.get('name')
                    db_type = db.get('type')
                    connNum = db.get('connNum')
                    connect_info = {}
                    connect = db.find('connect')
                    if connect is not None:
                        for param in connect.findall('param'):
                            connect_info[param.get('name')] = param.text
                        self.m_db_info[name] = {'connNum': connNum,'type': db_type,'connect': connect_info}

            # 获取sql_set下所有sql_cluster的信息
            self.m_sql_cluster_info = {}
            sql_set = root.find('sql_set')
            if sql_set is not None:
                for sql_cluster in sql_set.findall('sql_cluster'):
                    db_name = sql_cluster.get('db')
                    for sql in sql_cluster.findall('sql'):
                        sql_name = sql.get('name')
                        sql_text = sql.text.strip() if sql.text is not None else ''
                        self.m_sql_cluster_info[sql_name] = {'db_name': db_name, 'sqltext': sql_text}
                        
            logging2.info('init success')
            logging2.info('db connect info:%s'%(self.m_db_info))
            logging2.info('sql_cluster info:%s'%(self.m_sql_cluster_info))
            return True
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')
            return False


    def check_cfg_info(self):
        try:
            # 解析XML文件
            tree = ET.parse(self.cfg_xml_file)
            root = tree.getroot()

            db_names = []
            sql_names = []
            db_duplicates = []
            sql_duplicates = []

            # 检查db_set中db的name是否重复
            db_set = root.find('db_set')
            if db_set is not None:
                for db in db_set.findall('db'):
                    db_name = db.get('name')
                    if db_name in db_names:
                        db_duplicates.append(db_name)
                    else:
                        db_names.append(db_name)

            # 检查sql_set中sql的name是否重复
            sql_set = root.find('sql_set')
            if sql_set is not None:
                for sql_cluster in sql_set.findall('sql_cluster'):
                    sqls = sql_cluster.findall('sql')
                    for sql in sqls:
                        sql_name = sql.get('name')
                        if sql_name in sql_names:
                            sql_duplicates.append(sql_name)
                        else:
                            sql_names.append(sql_name)

            if db_duplicates:
                logging2.error("发现重复的db name，重复的名称如下:")
                for dup in db_duplicates:
                    logging2.error(dup)
                return False
            else:
                logging2.debug("没有发现重复的db name")

            if sql_duplicates:
                logging2.error("发现重复的sql name，重复的名称如下:")
                for dup in sql_duplicates:
                    logging2.error(dup)
                return False
            else:
                logging2.debug("没有发现重复的sql name")
            return True
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')
            return False


    def connect_to_db(self, db_name, index=-1):
        try:
            if self.m_db_info.get(db_name) is None:
                logging2.error(f'{db_name} not found')
                return

            if self.m_db_info[db_name]['type'] == 'mysql' and self.m_db_info[db_name].get('connect') is not None:
                for i in range(0, int(self.m_db_info[db_name]['connect']['connNum'])):
                    conn = pymysql.connect(host=self.m_db_info[db_name]['connect']['host'],
                                            port=int(self.m_db_info[db_name]['connect']['port']),
                                            user=self.m_db_info[db_name]['connect']['user'],
                                            password=ASEUtil.decrypted(self.m_db_info[db_name]['connect']['password']),
                                            db=self.m_db_info[db_name]['connect']['db'])
                    if index == -1:
                        self.m_conn[db_name][i] = conn
                        self.m_conn_mark[db_name][i] = 0
                        logging2.info(f"{self.m_db_info[db_name]['type']} {db_name} [{i}] connect success")
                    else:
                        self.m_conn[db_name][index] = conn
                        self.m_conn_mark[db_name][index] = 0
                        logging2.error(f"{self.m_db_info[db_name]['type']} {db_name} [{index}] connect success")
                        return True
            elif self.m_db_info[db_name]['type'] == 'oracle' and self.m_db_info[db_name].get('connect') is not None:
                for i in range(0, int(self.m_db_info[db_name]['connect']['connNum'])):
                    conn = cx_Oracle.connect(self.m_db_info[db_name]['connect']['user'],
                                                ASEUtil.decrypted(self.m_db_info[db_name]['connect']['password']),
                                                self.m_db_info[db_name]['connect']['host'] + ':' + self.m_db_info[db_name]['connect']['port'] + '/' + self.m_db_info[db_name]['connect']['db'])
                    if index == -1:
                        self.m_conn[db_name][i] = conn
                        self.m_conn_mark[db_name][i] = 0
                        logging2.error(f"{self.m_db_info[db_name]['type']} {db_name} [{i}] connect success")
                    else:
                        self.m_conn[db_name][index] = conn
                        self.m_conn_mark[db_name][index] = 0
                        logging2.error(f"{self.m_db_info[db_name]['type']} {db_name} [{index}] connect success")
                        return True
            elif self.m_db_info[db_name]['type'] == 'pgsql' and self.m_db_info[db_name].get('connect') is not None:
                for i in range(0, int(self.m_db_info[db_name]['connect']['connNum'])):
                    conn = psycopg2.connect(host=self.m_db_info[db_name]['connect']['host'],
                                            port=int(self.m_db_info[db_name]['connect']['port']),
                                            user=self.m_db_info[db_name]['connect']['user'],
                                            password=ASEUtil.decrypted(self.m_db_info[db_name]['connect']['password']),
                                            dbname=self.m_db_info[db_name]['connect']['db'])
                    if index == -1:
                        self.m_conn[db_name][i] = conn
                        self.m_conn_mark[db_name][i] = 0
                        logging2.error(f"{self.m_db_info[db_name]['type']} {db_name} [{i}] connect success")
                    else:
                        self.m_conn[db_name][index] = conn
                        self.m_conn_mark[db_name][index] = 0
                        logging2.error(f"{self.m_db_info[db_name]['type']} {db_name} [{index}] connect success")
                        return True
            elif self.m_db_info[db_name]['type'] == 'doris' and self.m_db_info[db_name].get('connect') is not None:
                for i in range(0, int(self.m_db_info[db_name]['connect']['connNum'])):
                    conn = pymysql.connect(host=self.m_db_info[db_name]['connect']['host'],
                                            port=int(self.m_db_info[db_name]['connect']['port']),
                                            user=self.m_db_info[db_name]['connect']['user'],
                                            password=ASEUtil.decrypted(self.m_db_info[db_name]['connect']['password']),
                                            db=self.m_db_info[db_name]['connect']['db'])
                    if index == -1:
                        self.m_conn[db_name][i] = conn
                        self.m_conn_mark[db_name][i] = 0
                        logging2.error(f"{self.m_db_info[db_name]['type']} {db_name} [{i}] connect success")
                    else:
                        self.m_conn[db_name][index] = conn
                        self.m_conn_mark[db_name][index] = 0
                        logging2.error(f"{self.m_db_info[db_name]['type']} {db_name} [{index}] connect success")
                        return True
            else:
                logging2.error(f'error:db type {self.m_db_info[db_name]["type"]} not support')
                return False
            return True
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')
            return False


    def create_conn(self):
        try:
            self.m_conn = {}
            self.m_conn_mark = {}

            # 判断是否指定了数据库，没有，则全部连接
            if self.m_db_name is None:
                for db_name in self.m_db_info:
                    self.m_conn[db_name] = {}
                    self.m_conn_mark[db_name] = {}
                    self.connect_to_db(db_name)
            else:
                self.connect_to_db(self.m_db_name)
                self.m_conn[self.m_db_name] = {}
                self.m_conn_mark[self.m_db_name] = {}
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')


    def get_idle_conn(self, db_name):
        while True:
            for i in range(0, len(self.m_conn[db_name])):
                if self.m_conn_mark[db_name][i] == 0:
                    self.m_conn_mark[db_name][i] = 1
                    logging2.info(f'get {db_name} [{i}] idle conn')
                    return self.m_conn[db_name][i], i
            time.sleep(1)
            logging2.error(f'db {db_name} all conn is busy, wait for 1s')


    '''
        概述：该函数用于执行指定的SQL语句，并在执行失败时进行重试。

        参数：
        sql_name：字符串类型，表示要执行的SQL语句的名称。
        params：可选参数，表示SQL语句的参数，默认为None。
        retry：可选参数，表示重试次数，默认为0。

        返回值：
        select语句：返回查询结果，如果查询失败，会进行自动重连，返回None。
        其他语句：返回执行影响条数，如果执行失败，会进行自动重连，返回None。
        如果SQL语句执行失败且重试次数为0，会进行自动重连，返回None。
    '''
    def excute_sql(self, sql_name, params=None, retry=0):
        conn = None
        index = -2
        try:
            if self.m_sql_cluster_info.get(sql_name) is None:
                logging2.error(f'error:sql {sql_name} not found')
                return
            else:
                db_name = self.m_sql_cluster_info[sql_name]['db_name']
                if self.m_conn.get(db_name) is None:
                    logging2.error(f'error:db {db_name} not connect')
                    return
                else:
                    conn,index = self.get_idle_conn(db_name)
                    cursor = conn.cursor()
                    sqlText = self.m_sql_cluster_info[sql_name]['sqltext']
                    logging2.info(f'excute sql sql_name:{sql_name} sqlText:{sqlText} params:{params}')
                    if params is not None:
                        sqlText = sqlText.replace(':v', '%s')
                    # 判断是select语句，还是其他语句
                    if sqlText.lower().startswith('select'):
                        if params is not None:
                            cursor.execute(sqlText, params)
                        else:
                            cursor.execute(sqlText)
                        result = cursor.fetchall()
                        logging2.info(f'{sql_name} excute success')
                        self.m_conn_mark[db_name][index] = 0
                        return result
                    else:
                        if params is not None:
                            cursor.execute(sqlText, params)
                        else:
                            cursor.execute(sqlText)
                        result = cursor.rowcount #  获取受影响的行数
                        self.m_conn[db_name][index].commit()
                        logging2.error(f'{sql_name} excute success')
                        self.m_conn_mark[db_name][index] = 0 #  将数据库连接标记重置为0，表示连接可用
                        return result
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')
            self.reconnect(sql_name, index)
            if retry:
                return self.excute_sql(sql_name, params, retry-1) #  递归调用execute_sql方法，重试次数减1
            else:
                return None


    def reconnect(self, sql_name, index):
        try:
            if index == -2:
                logging2.error(f'no index, no need to reconnect')
                return
            db_name = self.m_sql_cluster_info[sql_name]['db_name']
            self.m_conn[db_name][index].close()
            self.connect_to_db(db_name, index)
            logging2.error(f'{db_name} [{index}] reconnect success')
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')


    def stop(self):
        try:
            for db_name in self.m_conn:
                for i in range(0, len(self.m_conn[db_name])):
                    self.m_conn[db_name][i].close()
                    self.m_conn_mark[db_name][i] = 0
                    logging2.info(f'{db_name} [{i}] close success')
        except Exception as e:
            logging2.error(f'error:{e} {traceback.print_exc()}')
