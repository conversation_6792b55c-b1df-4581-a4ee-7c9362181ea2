#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
测试配置热更新功能
"""

import socket
import json
import time
import shutil
import os

def send_test_message(model, body, port=8686):
    """发送测试消息"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('192.168.161.27', port))
        
        message = {
            "model": model,
            "body": body
        }
        
        sock.send(json.dumps(message).encode('utf-8'))
        print(f"发送消息: {message}")
        sock.close()
        return True
    except Exception as e:
        print(f"发送消息失败: {e}")
        return False

def update_config_file():
    """更新配置文件"""
    config_file = "cfg/metricsToKafka.cfg.xml"
    backup_file = "cfg/metricsToKafka.cfg.xml.bak"
    
    # 备份原配置文件
    shutil.copy(config_file, backup_file)
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加新的规则
    new_rule = '''        <group4>
            <param name='rule_name'>test-new-rule</param>
            <param name='kafka_topic'>test_new_topic</param>
        </group4>'''
    
    # 在direct_kafka_rule结束标签前插入新规则
    updated_content = content.replace('    </direct_kafka_rule>', 
                                    new_rule + '\n    </direct_kafka_rule>')
    
    # 写回配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print("配置文件已更新，添加了新规则: test-new-rule -> test_new_topic")

def restore_config_file():
    """恢复原配置文件"""
    config_file = "cfg/metricsToKafka.cfg.xml"
    backup_file = "cfg/metricsToKafka.cfg.xml.bak"
    
    if os.path.exists(backup_file):
        shutil.copy(backup_file, config_file)
        os.remove(backup_file)
        print("配置文件已恢复")

def test_config_hot_reload():
    """测试配置热更新"""
    print("=" * 50)
    print("配置热更新测试")
    print("=" * 50)
    
    # 1. 测试原有规则
    print("\n1. 测试原有规则...")
    send_test_message("chfp", "original_rule_test_1")
    send_test_message("openapi-1", "original_rule_test_2")
    time.sleep(2)
    
    # 2. 测试不存在的规则
    print("\n2. 测试尚不存在的规则...")
    send_test_message("test-new-rule", "new_rule_test_before_update")
    time.sleep(2)
    
    # 3. 更新配置文件
    print("\n3. 更新配置文件...")
    update_config_file()
    
    # 4. 等待配置重新加载（主程序每10秒检查一次配置）
    print("\n4. 等待配置重新加载（最多15秒）...")
    for i in range(15, 0, -1):
        print(f"等待 {i} 秒...")
        time.sleep(1)
    
    # 5. 测试新规则
    print("\n5. 测试新添加的规则...")
    success = send_test_message("test-new-rule", "new_rule_test_after_update")
    if success:
        print("新规则测试成功！配置热更新工作正常")
    else:
        print("新规则测试失败")
    
    # 6. 再次测试原有规则确保其仍然工作
    print("\n6. 确认原有规则仍然工作...")
    send_test_message("chfp", "original_rule_test_after_update")
    time.sleep(2)
    
    # 7. 恢复原配置文件
    print("\n7. 恢复原配置文件...")
    restore_config_file()
    
    # 8. 等待配置重新加载
    print("\n8. 等待配置恢复（最多15秒）...")
    for i in range(15, 0, -1):
        print(f"等待 {i} 秒...")
        time.sleep(1)
    
    # 9. 验证新规则已失效
    print("\n9. 验证新规则已失效...")
    send_test_message("test-new-rule", "new_rule_test_after_restore")
    
    print("\n配置热更新测试完成！")
    print("请检查程序日志确认配置更新和规则重新加载情况")

if __name__ == "__main__":
    print("注意: 请确保metricsToKafka服务已启动")
    print("启动命令: cd src && python3 metricsToKafka.py ../cfg/metricsToKafka.cfg.xml")
    print()
    
    input("按回车键开始配置热更新测试...")
    
    try:
        test_config_hot_reload()
    except KeyboardInterrupt:
        print("\n测试被中断")
        restore_config_file()
    except Exception as e:
        print(f"\n测试过程中出现异常: {e}")
        restore_config_file()