#!/usr/bin/python
# -*- coding: utf-8 -*-
# 消息处理器

import threading
import logging2
import queue
import time
import json
import traceback

class MetricsMessageProcessor:
    """消息处理器 - 负责解析JSON消息并根据规则转发到Kafka队列"""
    
    def __init__(self, configObj, thread_num, messageQueue, kafkaQueue):
        self.m_configObj = configObj
        self.m_thread_num = thread_num
        self.m_messageQueue = messageQueue
        self.m_kafkaQueue = kafkaQueue
        self.m_isRun = True
        self.m_threads = []
        self.m_direct_kafka_rules = {}
        self.load_rules()
    
    def load_rules(self):
        """加载direct_kafka_rule配置"""
        try:
            # 获取direct_kafka_rule配置
            rule_groups = self.m_configObj.get_group_value('direct_kafka_rule')
            if rule_groups:
                for rule_group in rule_groups:
                    rule_name = rule_group.get('rule_name')
                    kafka_topic = rule_group.get('kafka_topic')
                    if rule_name and kafka_topic:
                        self.m_direct_kafka_rules[rule_name] = kafka_topic
                        logging2.debug("加载规则: %s -> %s" % (rule_name, kafka_topic))
            
            logging2.info("共加载了 %d 个direct_kafka_rule规则" % len(self.m_direct_kafka_rules))
        except Exception as err:
            logging2.error("加载规则异常: %s" % str(err))
    
    def start(self):
        """启动消息处理线程"""
        try:
            for i in range(self.m_thread_num):
                thread = threading.Thread(target=self.process_messages, args=(i,))
                thread.daemon = True
                thread.start()
                self.m_threads.append(thread)
                logging2.debug("消息处理线程 %d 启动" % i)
            
            logging2.info("消息处理器启动成功，线程数: %d" % self.m_thread_num)
            return True
        except Exception as err:
            logging2.error("启动消息处理器异常: %s" % str(err))
            return False
    
    def stop(self):
        """停止消息处理器"""
        self.m_isRun = False
        for thread in self.m_threads:
            thread.join(timeout=5)
        logging2.info("消息处理器已停止")
    
    def process_messages(self, thread_id):
        """消息处理线程主循环"""
        logging2.debug("消息处理线程 %d 开始运行" % thread_id)
        
        while self.m_isRun:
            try:
                # 从队列获取消息，设置超时避免阻塞
                try:
                    message_data = self.m_messageQueue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 处理消息
                self.handle_message(message_data)
                
                # 标记任务完成
                self.m_messageQueue.task_done()
                
            except Exception as err:
                logging2.error("线程 %d 处理消息异常: %s" % (thread_id, str(err)))
                traceback.print_exc()
                time.sleep(0.1)
        
        logging2.debug("消息处理线程 %d 退出" % thread_id)
    
    def handle_message(self, message_data):
        """处理单个消息"""
        try:
            json_data = message_data['data']
            client_addr = message_data['client_addr']
            timestamp = message_data['timestamp']
            
            # 检查消息格式 - 必须包含model和body字段
            if 'model' not in json_data or 'body' not in json_data:
                logging2.warn("消息格式错误，缺少model或body字段，来自 %s:%s" % (client_addr[0], client_addr[1]))
                return
            
            model = json_data['model']
            body = json_data['body']
            
            # 根据model查找对应的kafka_topic
            kafka_topic = self.m_direct_kafka_rules.get(model)
            if not kafka_topic:
                logging2.warn("未找到model[%s]对应的kafka规则，来自 %s:%s" % (model, client_addr[0], client_addr[1]))
                return
            
            # 准备Kafka消息
            kafka_message = {
                'topic': kafka_topic,
                'data': body,  # 只发送body内容到Kafka
                'model': model,
                'client_addr': client_addr,
                'timestamp': timestamp
            }
            
            # 将消息放入Kafka队列
            try:
                self.m_kafkaQueue.put(kafka_message, timeout=5.0)
                logging2.debug("消息已放入Kafka队列: model=%s, topic=%s" % (model, kafka_topic))
            except queue.Full:
                logging2.error("Kafka队列已满，丢弃消息: model=%s" % model)
                
        except Exception as err:
            logging2.error("处理消息异常: %s" % str(err))
            traceback.print_exc()
    
    def reload_rules(self):
        """重新加载规则（支持配置热更新）"""
        try:
            old_rules_count = len(self.m_direct_kafka_rules)
            self.m_direct_kafka_rules.clear()
            self.load_rules()
            new_rules_count = len(self.m_direct_kafka_rules)
            logging2.info("规则重新加载完成: %d -> %d" % (old_rules_count, new_rules_count))
        except Exception as err:
            logging2.error("重新加载规则异常: %s" % str(err))