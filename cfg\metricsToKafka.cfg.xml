<metricsToKafka>
    <log><!--修改需要重启程序,loglevel除外-->
        <param name="logpath">./log</param>
        <param name="loglevel">DEBUG</param><!--DEBUG, INFO, WARNING, ERROR-->    
    </log>
    <common><!--修改需要重启程序-->
        <param name="workPidNums">2</param><!--处理任务的子进程数，默认值2-->
        <param name="recv_thread_num">3</param><!--消息接收线程池大小-->
        <param name="kafka_thread_num">4</param><!--写kafka的线程数，默认值4-->
        <param name="queue_size">1000</param><!--队列大小-->
    </common>
    <kafka><!--修改需要重启程序-->
        <param name="safe_cert">0</param><!--0-不认证，1-认证-->
        <param name="bootstrap_servers">**************:9092</param>
        <param name="security_protocol">SASL_PLAINTEXT</param>
        <param name="sasl_mechanism">PLAIN</param>
        <param name="sasl_plain_username">itete</param>
        <param name="sasl_plain_password">xH9%iO2*tK23aR9f</param>
    </kafka>
    <server><!--修改需要重启程序-->
        <group1>
            <param name="server_ip">**************</param>
            <param name="server_port">8686</param>
        </group1>
        <group2>
            <param name="server_ip">**************</param>
            <param name="server_port">8688</param>
        </group2>
    </server>

    <direct_kafka_rule><!--修改不需要重启程序-->
        <group1>
            <param name='rule_name'>openapi-1</param><!--跟发送端保持一样-->
            <param name='kafka_topic'>hostAlive_topic-1</param><!--kafka的topic名-->
        </group1>
        <group2>
            <param name='rule_name'>openapi-2</param><!--跟发送端保持一样-->
            <param name='kafka_topic'>hostAlive_topic-2</param><!--kafka的topic名-->
        </group2>
        <group3>
            <param name='rule_name'>chfp</param>
            <param name='kafka_topic'>chfp_topic</param>
        </group3>
    </direct_kafka_rule>
</metricsToKafka>