#!/usr/bin/python
# -*- coding: utf-8 -*-
# 子进程连接管理器

import asyncio
import threading
import logging2
import time
import traceback
import json
import signal
import queue
from DCConfig import DCConfig
from MetricsMessageProcessor import MetricsMessageProcessor
from MetricsKafkaWriter import MetricsKafkaWriter

class MetricsConnectionManager():
    """子进程连接管理器"""
    def __init__(self, cfgFile):
        self.m_isRun = True
        self.m_cfgFile = cfgFile
        self.m_configObj = None
        self.m_messageQueue = None
        self.m_kafkaQueue = None
        self.m_messageProcessor = None
        self.m_kafkaWriter = None
        self.m_moduleName = "RECV"
    
    def handle_sigterm(self, signum, frame):
        logging2.warn("子进程收到SIGTERM信号，优雅关闭。")
        self.setRunState(False)

    def setRunState(self, isRun):
        self.m_isRun = isRun

    def init_child_process(self, conn):
        """初始化子进程"""
        try:
            config_node = ['log', 'common', 'kafka', 'server', 'direct_kafka_rule']
            self.m_configObj = DCConfig(self.m_cfgFile, config_node)

            # 启动日志
            logging2.start(self.m_configObj.get_single_value('log', 'logpath'), 
                          'metricsC', self.m_configObj.get_single_value('log', 'loglevel'))
            logging2.debug("子进程启动!")

            signal.signal(signal.SIGINT, self.handle_sigterm)
            signal.signal(signal.SIGQUIT, self.handle_sigterm)
            signal.signal(signal.SIGTERM, self.handle_sigterm)

            asyncio.run(self.child_process(conn))
        except Exception as err:
            traceback.print_exc()
            logging2.error("子进程初始化异常: %s" % str(err))                
        finally:
            return

    async def child_process(self, conn):   
        """子进程主循环"""
        loop = asyncio.get_running_loop()
        tasks = []
        try:
            if not self.startProcessors():
                logging2.error("启动处理器失败")
                self.m_isRun = False

            while self.m_isRun:
                # 从管道接收数据
                data = await loop.run_in_executor(None, conn.recv)
                if not data:
                    logging2.warn("子进程停止..")
                    break
                
                # 检查是否是配置更新信号
                if isinstance(data, tuple) and len(data) == 2:
                    signal_type, signal_data = data
                    if signal_type == "CONFIG_UPDATE":
                        logging2.info("收到配置更新信号，重新加载配置")
                        # 重新加载配置并通知消息处理器
                        self.m_configObj.update_config()
                        if self.m_messageProcessor:
                            self.m_messageProcessor.reload_rules()
                        continue
                    elif not signal_data:  # 退出信号
                        logging2.warn("收到退出信号")
                        break
                
                # 处理TCP连接数据
                client_socket, addr = data
                if not client_socket:
                    logging2.warn("收到无效连接")
                    break
                    
                logging2.warn("从父进程接收到连接[%s:%s]" % (addr[0], addr[1]))
                # 使用异步事件循环处理客户端连接
                reader, writer = await asyncio.open_connection(sock=client_socket)
                task = asyncio.create_task(self.handle_client(reader, writer, addr))
                tasks.append(task)
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error("子进程异常: %s" % stack_trace)           
        finally:
            for task in tasks:
                task.cancel()
            await asyncio.gather(*tasks, return_exceptions=True)
            self.setRunState(False)
            self.exitDeal()
            logging2.stop() 
            logging2.warn("子进程退出")

    def startProcessors(self):
        """启动消息处理器和Kafka写入器"""
        try:
            queue_size = int(self.m_configObj.get_single_value('common', 'queue_size'))
            recv_thread_num = int(self.m_configObj.get_single_value('common', 'recv_thread_num'))
            kafka_thread_num = int(self.m_configObj.get_single_value('common', 'kafka_thread_num'))
            
            # 创建队列
            self.m_messageQueue = queue.Queue(queue_size)
            self.m_kafkaQueue = queue.Queue(queue_size)
            
            # 创建消息处理器
            self.m_messageProcessor = MetricsMessageProcessor(
                self.m_configObj, recv_thread_num, self.m_messageQueue, self.m_kafkaQueue)
            if not self.m_messageProcessor.start():
                logging2.error("启动消息处理器失败")
                return False
            
            # 创建Kafka写入器
            self.m_kafkaWriter = MetricsKafkaWriter(
                self.m_configObj, kafka_thread_num, self.m_kafkaQueue)
            if not self.m_kafkaWriter.start():
                logging2.error("启动Kafka写入器失败")
                return False
            
            logging2.info("所有处理器启动成功")
            return True
        except Exception as err:
            logging2.error("启动处理器异常: %s" % str(err))
            return False

    async def handle_client(self, reader, writer, addr):
        """处理客户端连接"""
        try:
            logging2.debug("开始处理客户端连接 %s:%s" % (addr[0], addr[1]))
            
            while self.m_isRun:
                # 读取数据，设置超时
                try:
                    data = await asyncio.wait_for(reader.read(8192), timeout=30.0)
                    if not data:
                        logging2.debug("客户端 %s:%s 断开连接" % (addr[0], addr[1]))
                        break
                    
                    # 解码数据
                    message = data.decode('utf-8').strip()
                    if not message:
                        continue
                    
                    logging2.debug("收到消息: %s" % message)
                    
                    # 验证JSON格式
                    try:
                        json_data = json.loads(message)
                        # 将消息放入处理队列
                        self.m_messageQueue.put({
                            'data': json_data,
                            'client_addr': addr,
                            'timestamp': time.time()
                        })
                        logging2.debug("消息已放入处理队列")
                    except json.JSONDecodeError as e:
                        logging2.warn("无效的JSON格式，来自 %s:%s: %s" % (addr[0], addr[1], str(e)))
                        continue
                        
                except asyncio.TimeoutError:
                    logging2.debug("客户端 %s:%s 读取超时" % (addr[0], addr[1]))
                    continue
                except Exception as e:
                    logging2.error("读取客户端数据异常 %s:%s: %s" % (addr[0], addr[1], str(e)))
                    break
                    
        except Exception as err:
            logging2.error("处理客户端连接异常 %s:%s: %s" % (addr[0], addr[1], str(err)))
        finally:
            try:
                writer.close()
                await writer.wait_closed()
                logging2.debug("客户端连接 %s:%s 已关闭" % (addr[0], addr[1]))
            except:
                pass

    def exitDeal(self):
        """退出处理"""
        try:
            if self.m_messageProcessor:
                self.m_messageProcessor.stop()
            if self.m_kafkaWriter:
                self.m_kafkaWriter.stop()
            logging2.info("所有处理器已停止")
        except Exception as err:
            logging2.error("退出处理异常: %s" % str(err))