#!/bin/bash

# metricsToKafka 编译和安装脚本

echo "======================================="
echo "metricsToKafka 安装脚本"
echo "======================================="

# 检查Python版本
python_version=$(python3 --version 2>&1)
if [ $? -ne 0 ]; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi
echo "Python版本: $python_version"

# 检查必要的Python包
echo "检查Python依赖包..."

# 检查kafka-python
python3 -c "import kafka" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "安装kafka-python..."
    pip3 install kafka-python
    if [ $? -ne 0 ]; then
        echo "错误: kafka-python安装失败"
        exit 1
    fi
fi

# 检查pycrypto
python3 -c "from Crypto.Cipher import AES" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "安装pycrypto..."
    pip3 install pycrypto
    if [ $? -ne 0 ]; then
        echo "尝试安装pycryptodome..."
        pip3 install pycryptodome
        if [ $? -ne 0 ]; then
            echo "错误: 加密库安装失败"
            exit 1
        fi
    fi
fi

# 创建必要的目录
echo "创建目录结构..."
mkdir -p log
mkdir -p data

# 设置权限
chmod +x src/metricsToKafka.py
chmod +x make.sh

echo "======================================="
echo "安装完成!"
echo "======================================="
echo "使用方法:"
echo "  启动: cd src && python3 metricsToKafka.py ../cfg/metricsToKafka.cfg.xml"
echo "  停止: kill -TERM <pid>"
echo "  配置文件: cfg/metricsToKafka.cfg.xml"
echo "  日志目录: ./log/"
echo "======================================="