#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
Kafka消息验证工具
用于验证metricsToKafka发送的消息是否真正写入了Kafka Topic
"""

import sys
import json
import time
import argparse
from datetime import datetime

try:
    from kafka import KafkaConsumer, KafkaProducer, TopicPartition
    from kafka.errors import KafkaError
except ImportError:
    print("错误：未安装kafka-python库")
    print("请安装：pip install kafka-python")
    sys.exit(1)

class KafkaVerificationTool:
    def __init__(self, bootstrap_servers, use_auth=False, username=None, password=None):
        """初始化Kafka验证工具"""
        self.bootstrap_servers = bootstrap_servers.split(',')
        
        # 基础配置
        self.consumer_config = {
            'bootstrap_servers': self.bootstrap_servers,
            'auto_offset_reset': 'earliest',
            'enable_auto_commit': False,
            'value_deserializer': lambda x: x.decode('utf-8') if x else None,
            'key_deserializer': lambda x: x.decode('utf-8') if x else None,
            'consumer_timeout_ms': 5000  # 5秒超时
        }
        
        self.producer_config = {
            'bootstrap_servers': self.bootstrap_servers,
            'value_serializer': lambda v: str(v).encode('utf-8'),
            'key_serializer': lambda k: str(k).encode('utf-8') if k else None,
        }
        
        # 如果需要认证
        if use_auth and username and password:
            auth_config = {
                'security_protocol': 'SASL_PLAINTEXT',
                'sasl_mechanism': 'PLAIN',
                'sasl_plain_username': username,
                'sasl_plain_password': password
            }
            self.consumer_config.update(auth_config)
            self.producer_config.update(auth_config)
    
    def list_topics(self):
        """列出所有可用的Topic"""
        print("🔍 正在获取Topic列表...")
        try:
            consumer = KafkaConsumer(**self.consumer_config)
            topics = consumer.topics()
            consumer.close()
            
            print(f"📋 发现 {len(topics)} 个Topic:")
            for i, topic in enumerate(sorted(topics), 1):
                print(f"  {i}. {topic}")
            return sorted(topics)
        except Exception as e:
            print(f"❌ 获取Topic列表失败: {e}")
            return []
    
    def get_topic_info(self, topic):
        """获取Topic的详细信息"""
        print(f"📊 获取Topic '{topic}' 信息...")
        try:
            consumer = KafkaConsumer(**self.consumer_config)
            partitions = consumer.partitions_for_topic(topic)
            
            if partitions:
                print(f"  分区数: {len(partitions)}")
                
                # 获取每个分区的offset信息
                topic_partitions = [TopicPartition(topic, p) for p in partitions]
                
                # 获取最早和最新的offset
                beginning_offsets = consumer.beginning_offsets(topic_partitions)
                end_offsets = consumer.end_offsets(topic_partitions)
                
                total_messages = 0
                for tp in topic_partitions:
                    start_offset = beginning_offsets[tp]
                    end_offset = end_offsets[tp]
                    message_count = end_offset - start_offset
                    total_messages += message_count
                    print(f"  分区 {tp.partition}: {start_offset} - {end_offset} (共 {message_count} 条消息)")
                
                print(f"  📈 总消息数: {total_messages}")
                return total_messages
            else:
                print(f"  ⚠️  Topic '{topic}' 不存在或无分区")
                return 0
        except Exception as e:
            print(f"❌ 获取Topic信息失败: {e}")
            return 0
        finally:
            consumer.close()
    
    def consume_messages(self, topic, max_messages=10, show_content=True, commit_offset=False, group_id=None):
        """消费Topic中的消息"""
        action = "消费并提交" if commit_offset else "读取"
        print(f"🔄 开始{action}Topic '{topic}' 的消息 (最多 {max_messages} 条)...")
        
        try:
            consumer_config = self.consumer_config.copy()
            
            if commit_offset:
                # 真正消费消息的配置
                consumer_config['enable_auto_commit'] = True
                consumer_config['auto_commit_interval_ms'] = 1000
                consumer_config['group_id'] = group_id or 'consumer_group_verification'
                print(f"🔥 使用消费者组: {consumer_config['group_id']} (将提交offset)")
            else:
                # 只读取不消费的配置
                consumer_config['group_id'] = f'verification_tool_{int(time.time())}'
                print("👀 只读模式 (不会提交offset)")
            
            consumer = KafkaConsumer(topic, **consumer_config)
            
            message_count = 0
            start_time = time.time()
            
            for message in consumer:
                message_count += 1
                timestamp = datetime.fromtimestamp(message.timestamp / 1000) if message.timestamp else "Unknown"
                
                print(f"\n📨 消息 #{message_count}:")
                print(f"  Topic: {message.topic}")
                print(f"  分区: {message.partition}")
                print(f"  Offset: {message.offset}")
                print(f"  Key: {message.key}")
                print(f"  时间戳: {timestamp}")
                
                if show_content:
                    print(f"  内容: {message.value}")
                else:
                    content_preview = str(message.value)[:100] + "..." if len(str(message.value)) > 100 else str(message.value)
                    print(f"  内容预览: {content_preview}")
                
                if commit_offset:
                    print(f"  ✅ 已标记为已消费")
                
                print("-" * 60)
                
                if message_count >= max_messages:
                    break
            
            if commit_offset and message_count > 0:
                # 手动提交offset确保消息被标记为已消费
                consumer.commit()
                print(f"🎯 已提交offset，{message_count} 条消息已被消费")
            
            consumer.close()
            
            elapsed_time = time.time() - start_time
            action_result = "消费" if commit_offset else "读取"
            print(f"\n✅ {action_result}完成: 共处理 {message_count} 条消息，耗时 {elapsed_time:.2f} 秒")
            
        except Exception as e:
            print(f"❌ {action}消息失败: {e}")
    
    def consume_and_delete(self, topic, max_messages=10, group_id=None):
        """消费并删除消息（真正的消费）"""
        print(f"🗑️  开始消费并删除Topic '{topic}' 的消息...")
        return self.consume_messages(topic, max_messages, True, True, group_id)
    
    def clear_topic_messages(self, topic, group_id=None):
        """清空Topic中的所有消息"""
        print(f"🧹 开始清空Topic '{topic}' 的所有消息...")
        print("⚠️  这将消费掉Topic中的所有现有消息!")
        
        confirm = input("确认要清空所有消息吗？(输入 'yes' 确认): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return
        
        try:
            consumer_config = self.consumer_config.copy()
            consumer_config['enable_auto_commit'] = True
            consumer_config['auto_commit_interval_ms'] = 1000
            consumer_config['group_id'] = group_id or f'clear_topic_{int(time.time())}'
            
            consumer = KafkaConsumer(topic, **consumer_config)
            
            message_count = 0
            start_time = time.time()
            
            print("🔄 正在消费消息...")
            for message in consumer:
                message_count += 1
                if message_count % 100 == 0:
                    print(f"已处理 {message_count} 条消息...")
            
            # 确保提交offset
            consumer.commit()
            consumer.close()
            
            elapsed_time = time.time() - start_time
            print(f"\n✅ 清空完成: 共消费 {message_count} 条消息，耗时 {elapsed_time:.2f} 秒")
            
        except Exception as e:
            print(f"❌ 清空失败: {e}")
    
    def send_test_message(self, topic, model, test_data):
        """发送测试消息（模拟metricsToKafka的格式）"""
        print(f"📤 发送测试消息到Topic '{topic}'...")
        
        try:
            producer = KafkaProducer(**self.producer_config)
            
            # 模拟metricsToKafka的消息格式（只发送body部分）
            test_message = f"test_verification_{int(time.time())}|{test_data}"
            
            # 发送消息，key为model，value为body内容
            future = producer.send(topic, value=test_message, key=model)
            
            # 等待发送结果
            record_metadata = future.get(timeout=30)
            
            print(f"✅ 测试消息发送成功!")
            print(f"  Topic: {record_metadata.topic}")
            print(f"  分区: {record_metadata.partition}")
            print(f"  Offset: {record_metadata.offset}")
            print(f"  Key: {model}")
            print(f"  Value: {test_message}")
            
            producer.flush()
            producer.close()
            
            return True
            
        except Exception as e:
            print(f"❌ 发送测试消息失败: {e}")
            return False
    
    def monitor_topic_realtime(self, topic, duration=60):
        """实时监控Topic的新消息"""
        print(f"👁️  实时监控Topic '{topic}' (持续 {duration} 秒)...")
        print("按 Ctrl+C 停止监控")
        
        try:
            consumer_config = self.consumer_config.copy()
            consumer_config['auto_offset_reset'] = 'latest'  # 只消费新消息
            consumer_config['group_id'] = f'realtime_monitor_{int(time.time())}'
            consumer_config['consumer_timeout_ms'] = 1000  # 1秒超时
            
            consumer = KafkaConsumer(topic, **consumer_config)
            
            start_time = time.time()
            message_count = 0
            
            while time.time() - start_time < duration:
                try:
                    for message in consumer:
                        message_count += 1
                        timestamp = datetime.fromtimestamp(message.timestamp / 1000) if message.timestamp else "Unknown"
                        
                        print(f"\n🆕 新消息 #{message_count} @ {timestamp}")
                        print(f"  分区: {message.partition}, Offset: {message.offset}")
                        print(f"  Key: {message.key}")
                        print(f"  Value: {message.value}")
                        print("-" * 40)
                        
                        if time.time() - start_time >= duration:
                            break
                except:
                    # 消费超时，继续监控
                    print(".", end="", flush=True)
                    continue
            
            consumer.close()
            print(f"\n🏁 监控结束: 共接收到 {message_count} 条新消息")
            
        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")
        except Exception as e:
            print(f"\n❌ 监控异常: {e}")

def main():
    parser = argparse.ArgumentParser(description='Kafka消息验证工具')
    parser.add_argument('--servers', default='**************:9092', 
                       help='Kafka服务器地址 (默认: **************:9092)')
    parser.add_argument('--auth', action='store_true', 
                       help='启用SASL认证')
    parser.add_argument('--username', default='itete', 
                       help='SASL用户名 (默认: itete)')
    parser.add_argument('--password', default='xH9%iO2*tK23aR9f', 
                       help='SASL密码')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出Topics
    subparsers.add_parser('list', help='列出所有Topic')
    
    # Topic信息
    info_parser = subparsers.add_parser('info', help='获取Topic信息')
    info_parser.add_argument('topic', help='Topic名称')
    
    # 消费消息
    consume_parser = subparsers.add_parser('consume', help='消费Topic消息')
    consume_parser.add_argument('topic', help='Topic名称')
    consume_parser.add_argument('--max', type=int, default=10, help='最大消息数')
    consume_parser.add_argument('--no-content', action='store_true', help='不显示消息内容')
    consume_parser.add_argument('--commit', action='store_true', help='提交offset (真正消费掉消息)')
    consume_parser.add_argument('--group-id', help='消费者组ID')
    
    # 消费并删除消息
    delete_parser = subparsers.add_parser('delete', help='消费并删除消息')
    delete_parser.add_argument('topic', help='Topic名称')
    delete_parser.add_argument('--max', type=int, default=10, help='最大消息数')
    delete_parser.add_argument('--group-id', help='消费者组ID')
    
    # 清空Topic
    clear_parser = subparsers.add_parser('clear', help='清空Topic所有消息')
    clear_parser.add_argument('topic', help='Topic名称')
    clear_parser.add_argument('--group-id', help='消费者组ID')
    
    # 发送测试消息
    send_parser = subparsers.add_parser('send', help='发送测试消息')
    send_parser.add_argument('topic', help='Topic名称')
    send_parser.add_argument('--model', default='test', help='Model值 (key)')
    send_parser.add_argument('--data', default='test_data', help='测试数据')
    
    # 实时监控
    monitor_parser = subparsers.add_parser('monitor', help='实时监控Topic')
    monitor_parser.add_argument('topic', help='Topic名称')
    monitor_parser.add_argument('--duration', type=int, default=60, help='监控时长(秒)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🚀 Kafka验证工具启动")
    print(f"📡 连接服务器: {args.servers}")
    if args.auth:
        print(f"🔐 使用认证: {args.username}")
    print("-" * 50)
    
    # 创建工具实例
    tool = KafkaVerificationTool(
        bootstrap_servers=args.servers,
        use_auth=args.auth,
        username=args.username if args.auth else None,
        password=args.password if args.auth else None
    )
    
    # 执行对应命令
    try:
        if args.command == 'list':
            tool.list_topics()
        
        elif args.command == 'info':
            tool.get_topic_info(args.topic)
        
        elif args.command == 'consume':
            tool.consume_messages(args.topic, args.max, not args.no_content, args.commit, args.group_id)
        
        elif args.command == 'delete':
            tool.consume_and_delete(args.topic, args.max, args.group_id)
        
        elif args.command == 'clear':
            tool.clear_topic_messages(args.topic, args.group_id)
        
        elif args.command == 'send':
            tool.send_test_message(args.topic, args.model, args.data)
        
        elif args.command == 'monitor':
            tool.monitor_topic_realtime(args.topic, args.duration)
    
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")

if __name__ == "__main__":
    main()