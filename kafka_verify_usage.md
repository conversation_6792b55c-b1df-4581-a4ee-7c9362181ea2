# Kafka验证工具使用说明

## 安装依赖

```bash
pip install kafka-python
```

## 基本用法

### 1. 列出所有Topic
```bash
python kafka_verification_tool.py list
```

### 2. 查看Topic信息
```bash
python kafka_verification_tool.py info chfp_topic
python kafka_verification_tool.py info hostAlive_topic-1
python kafka_verification_tool.py info hostAlive_topic-2
```

### 3. 查看消息内容（不消费）
```bash
# 查看最新10条消息（只读，不会消费掉）
python kafka_verification_tool.py consume chfp_topic

# 查看最新50条消息
python kafka_verification_tool.py consume chfp_topic --max 50

# 只看消息概要，不显示完整内容
python kafka_verification_tool.py consume chfp_topic --no-content
```

### 4. 真正消费掉消息
```bash
# 消费并删除10条消息（真正的消费）
python kafka_verification_tool.py consume chfp_topic --max 10 --commit

# 指定消费者组消费消息
python kafka_verification_tool.py consume chfp_topic --max 10 --commit --group-id my_consumer_group

# 消费并删除消息的简化命令
python kafka_verification_tool.py delete chfp_topic --max 10

# 清空Topic所有消息（危险操作！）
python kafka_verification_tool.py clear chfp_topic
```

### 5. 发送测试消息
```bash
# 向chfp_topic发送测试消息
python kafka_verification_tool.py send chfp_topic --model chfp --data "test_data_123"

# 向hostAlive_topic-1发送测试消息
python kafka_verification_tool.py send hostAlive_topic-1 --model openapi-1 --data "alive_test"
```

### 6. 实时监控新消息
```bash
# 监控60秒
python kafka_verification_tool.py monitor chfp_topic

# 监控300秒（5分钟）
python kafka_verification_tool.py monitor chfp_topic --duration 300
```

## 认证模式（如果safe_cert=1）

```bash
# 使用认证
python kafka_verification_tool.py --auth list
python kafka_verification_tool.py --auth consume chfp_topic
python kafka_verification_tool.py --auth monitor chfp_topic
```

## 完整验证流程

### 步骤1：检查Topic状态
```bash
python kafka_verification_tool.py list
python kafka_verification_tool.py info chfp_topic
```

### 步骤2：发送测试消息
```bash
python kafka_verification_tool.py send chfp_topic --model chfp --data "verification_test"
```

### 步骤3：验证消息是否收到
```bash
python kafka_verification_tool.py consume chfp_topic --max 1
```

### 步骤4：实时监控（在另一个终端）
```bash
# 终端1：监控新消息
python kafka_verification_tool.py monitor chfp_topic

# 终端2：使用metricsToKafka的test_simple.py发送消息
python test_simple.py
```

## 常见问题

### 1. 连接失败
- 检查Kafka服务器地址是否正确
- 检查网络连接
- 确认是否需要认证

### 2. 认证失败
- 确认用户名密码正确
- 检查配置文件中的safe_cert设置

### 3. Topic不存在
- 确认Topic名称拼写正确
- 检查metricsToKafka配置文件中的规则设置

### 4. 没有消息
- 确认metricsToKafka服务正在运行
- 检查客户端是否成功发送消息到metricsToKafka服务
- 查看metricsToKafka的日志文件

## 消费 vs 读取的区别

### 只读取（默认行为）
```bash
python kafka_verification_tool.py consume chfp_topic
```
- 🔍 **只查看**消息内容，不会删除
- 👀 每次运行都能看到相同的消息
- 🔄 使用临时消费者组，不影响其他消费者

### 真正消费（删除消息）
```bash
python kafka_verification_tool.py consume chfp_topic --commit
# 或使用简化命令
python kafka_verification_tool.py delete chfp_topic
```
- 🗑️ **消费并删除**消息，提交offset
- ⚠️ 消息被消费后，其他消费者组无法再读取到
- 📊 使用固定消费者组名，保持消费进度

### 清空所有消息
```bash
python kafka_verification_tool.py clear chfp_topic
```
- 🧹 **删除Topic中所有现有消息**
- ⚠️ **危险操作**：会要求确认输入 'yes'
- 🔄 适用于测试环境清理数据

## 实际使用场景

### 场景1：验证消息内容
```bash
# 只查看，不删除
python kafka_verification_tool.py consume chfp_topic --max 5
```

### 场景2：清理测试消息
```bash
# 删除最近10条测试消息
python kafka_verification_tool.py delete chfp_topic --max 10
```

### 场景3：完全清空Topic
```bash
# 清空所有消息（需要确认）
python kafka_verification_tool.py clear chfp_topic
```

### 场景4：监控 + 消费组合
```bash
# 终端1：实时监控新消息
python kafka_verification_tool.py monitor chfp_topic

# 终端2：消费掉旧消息
python kafka_verification_tool.py delete chfp_topic --max 100
```