--mysql表，单片表
CREATE TABLE `ops_sys_kpi_detail_def` (
  `ID` bigint(16) NOT NULL AUTO_INCREMENT,
  `module` varchar(64) NOT NULL COMMENT '模块名称',
  `kpi_name` varchar(64) NOT NULL COMMENT '指标名称',
  `kpi_id` bigint(16) DEFAULT NULL COMMENT '指标id,与ops_sys_kpi_def对应指标ID关联',
  `kpi_code` varchar(64) NOT NULL COMMENT '指标码,对应ops_sys_kpi_detail表字段',
  `kpi_type` int(5) DEFAULT NULL COMMENT '指标类型:1:cycle,2:state,0:明细记录前缀',
  `kpi_desc` varchar(255) DEFAULT NULL COMMENT '指标名称描述',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--doris表，聚合表
CREATE TABLE IF NOT EXISTS ops_sys_kpi_detail (
    `module` VARCHAR(64) not null COMMENT '模块',
	 batId VARCHAR(16) not null COMMENT '批次',
	`Host` VARCHAR(64) not null default 'unknown_host' COMMENT '主机IP',
	 latnid VARCHAR(8) not null default '888' COMMENT '本地网',
	 PID VARCHAR(32) not null default '-1' COMMENT '进程Id',
	`system` VARCHAR(64) not null COMMENT '系统',
    subsys VARCHAR(64) not null COMMENT '子系统',
	`month` TINYINT not null ,
	KpiCycle1 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle2 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle3 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle4 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle5 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle6 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle7 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle8 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle9 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle10 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle11 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle12 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle13 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle14 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle15 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle16 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle17 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle18 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle19 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle20 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle21 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle22 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle23 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle24 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle25 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle26 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle27 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle28 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle29 BIGINT(19) SUM DEFAULT NULL,
    KpiCycle30 BIGINT(19) SUM DEFAULT NULL,
	KpiState1 VARCHAR(64) REPLACE default null ,
	KpiState2 VARCHAR(64) REPLACE default null ,
	KpiState3 VARCHAR(64) REPLACE default null ,
	KpiState4 VARCHAR(64) REPLACE default null ,
	KpiState5 VARCHAR(64) REPLACE default null ,
	KpiState6 VARCHAR(64) REPLACE default null ,
	KpiState7 VARCHAR(64) REPLACE default null ,
	KpiState8 VARCHAR(64) REPLACE default null ,
	KpiState9 VARCHAR(64) REPLACE default null ,
	KpiState10 VARCHAR(64) REPLACE default null ,
	createDate DATETIME REPLACE NOT NULL DEFAULT CURRENT_TIMESTAMP  
) ENGINE = OLAP 
AGGREGATE KEY(`module`,batId,`Host`,latnid,PID,`system`,subsys,`month`)
COMMENT '指标输出明细表' 
PARTITION BY RANGE (month) (  -- 按月期分区
    PARTITION p01 VALUES LESS THAN ("2"),
    PARTITION p02 VALUES LESS THAN ("3"),
    PARTITION p03 VALUES LESS THAN ("4"),
    PARTITION p04 VALUES LESS THAN ("5"),
	PARTITION p05 VALUES LESS THAN ("6"),
	PARTITION p06 VALUES LESS THAN ("7"),
	PARTITION p07 VALUES LESS THAN ("8"),
	PARTITION p08 VALUES LESS THAN ("9"),
	PARTITION p09 VALUES LESS THAN ("10"),
	PARTITION p10 VALUES LESS THAN ("11"),
	PARTITION p11 VALUES LESS THAN ("12"),
	PARTITION p12 VALUES LESS THAN ("13")
)
DISTRIBUTED BY HASH (`batId`) BUCKETS 32 PROPERTIES (
	"replication_allocation" = "tag.location.default: 1",
	"in_memory" = "false",
	"storage_format" = "V2",
	"disable_auto_compaction" = "false"
);
