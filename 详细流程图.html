<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 628.765625 2521.0751953125" style="max-width: 628.765625px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-1754291466506-cfp84r9cj"><style>#mermaid-svg-1754291466506-cfp84r9cj{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#616161;}#mermaid-svg-1754291466506-cfp84r9cj .error-icon{fill:#f2dede;}#mermaid-svg-1754291466506-cfp84r9cj .error-text{fill:#a1260d;stroke:#a1260d;}#mermaid-svg-1754291466506-cfp84r9cj .edge-thickness-normal{stroke-width:2px;}#mermaid-svg-1754291466506-cfp84r9cj .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-1754291466506-cfp84r9cj .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-1754291466506-cfp84r9cj .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-1754291466506-cfp84r9cj .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-1754291466506-cfp84r9cj .marker{fill:#616161;stroke:#616161;}#mermaid-svg-1754291466506-cfp84r9cj .marker.cross{stroke:#616161;}#mermaid-svg-1754291466506-cfp84r9cj svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-1754291466506-cfp84r9cj .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#616161;}#mermaid-svg-1754291466506-cfp84r9cj .cluster-label text{fill:#424242;}#mermaid-svg-1754291466506-cfp84r9cj .cluster-label span,#mermaid-svg-1754291466506-cfp84r9cj p{color:#424242;}#mermaid-svg-1754291466506-cfp84r9cj .label text,#mermaid-svg-1754291466506-cfp84r9cj span,#mermaid-svg-1754291466506-cfp84r9cj p{fill:#616161;color:#616161;}#mermaid-svg-1754291466506-cfp84r9cj .node rect,#mermaid-svg-1754291466506-cfp84r9cj .node circle,#mermaid-svg-1754291466506-cfp84r9cj .node ellipse,#mermaid-svg-1754291466506-cfp84r9cj .node polygon,#mermaid-svg-1754291466506-cfp84r9cj .node path{fill:#fdf6e3;stroke:#d3af86;stroke-width:1px;}#mermaid-svg-1754291466506-cfp84r9cj .flowchart-label text{text-anchor:middle;}#mermaid-svg-1754291466506-cfp84r9cj .node .label{text-align:center;}#mermaid-svg-1754291466506-cfp84r9cj .node.clickable{cursor:pointer;}#mermaid-svg-1754291466506-cfp84r9cj .arrowheadPath{fill:#02091c;}#mermaid-svg-1754291466506-cfp84r9cj .edgePath .path{stroke:#616161;stroke-width:2.0px;}#mermaid-svg-1754291466506-cfp84r9cj .flowchart-link{stroke:#616161;fill:none;}#mermaid-svg-1754291466506-cfp84r9cj .edgeLabel{background-color:#fdf6e399;text-align:center;}#mermaid-svg-1754291466506-cfp84r9cj .edgeLabel rect{opacity:0.5;background-color:#fdf6e399;fill:#fdf6e399;}#mermaid-svg-1754291466506-cfp84r9cj .labelBkg{background-color:rgba(253, 246, 227, 0.5);}#mermaid-svg-1754291466506-cfp84r9cj .cluster rect{fill:rgba(238, 232, 213, 0.5);stroke:#b49471;stroke-width:1px;}#mermaid-svg-1754291466506-cfp84r9cj .cluster text{fill:#424242;}#mermaid-svg-1754291466506-cfp84r9cj .cluster span,#mermaid-svg-1754291466506-cfp84r9cj p{color:#424242;}#mermaid-svg-1754291466506-cfp84r9cj div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:rgba(181, 137, 0, 0.67);border:1px solid #b49471;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-1754291466506-cfp84r9cj .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#616161;}#mermaid-svg-1754291466506-cfp84r9cj :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-svg-1754291466506-cfp84r9cj .startStyle&gt;*{fill:#c8e6c9!important;stroke:#388e3c!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .startStyle span{fill:#c8e6c9!important;stroke:#388e3c!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .processStyle&gt;*{fill:#e1f5fe!important;stroke:#0277bd!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .processStyle span{fill:#e1f5fe!important;stroke:#0277bd!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .decisionStyle&gt;*{fill:#fff3e0!important;stroke:#f57c00!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .decisionStyle span{fill:#fff3e0!important;stroke:#f57c00!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .updateStyle&gt;*{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .updateStyle span{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .endStyle&gt;*{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}#mermaid-svg-1754291466506-cfp84r9cj .endStyle span{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1754291466506-cfp84r9cj_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1754291466506-cfp84r9cj_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1754291466506-cfp84r9cj_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1754291466506-cfp84r9cj_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-Start LE-LoadConfig" id="L-Start-LoadConfig-0" d="M128.647,35.8L128.647,39.967C128.647,44.133,128.647,52.467,128.647,59.917C128.647,67.367,128.647,73.933,128.647,77.217L128.647,80.5"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-LoadConfig LE-StartMain" id="L-LoadConfig-StartMain-0" d="M128.647,121.6L128.647,125.767C128.647,129.933,128.647,138.267,128.647,145.717C128.647,153.167,128.647,159.733,128.647,163.017L128.647,166.3"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-StartMain LE-StartSM" id="L-StartMain-StartSM-0" d="M128.647,207.4L128.647,211.567C128.647,215.733,128.647,224.067,128.647,231.517C128.647,238.967,128.647,245.533,128.647,248.817L128.647,252.1"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-StartSM LE-StartTCP" id="L-StartSM-StartTCP-0" d="M103.924,293.2L98.169,297.367C92.414,301.533,80.904,309.867,75.149,317.317C69.394,324.767,69.394,331.333,69.394,334.617L69.394,337.9"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-StartSM LE-StartChild" id="L-StartSM-StartChild-0" d="M173.56,293.2L184.014,297.367C194.469,301.533,215.378,309.867,225.833,317.317C236.288,324.767,236.288,331.333,236.288,334.617L236.288,337.9"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-StartChild LE-StartMP" id="L-StartChild-StartMP-0" d="M236.288,379L236.288,383.167C236.288,387.333,236.288,395.667,236.288,403.117C236.288,410.567,236.288,417.133,236.288,420.417L236.288,423.7"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-StartMP LE-StartKW" id="L-StartMP-StartKW-0" d="M236.288,464.8L236.288,468.967C236.288,473.133,236.288,481.467,236.288,488.917C236.288,496.367,236.288,502.933,236.288,506.217L236.288,509.5"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-StartKW LE-MainLoop" id="L-StartKW-MainLoop-0" d="M236.288,550.6L236.288,554.767C236.288,558.933,236.288,567.267,236.354,574.8C236.42,582.334,236.552,589.067,236.618,592.434L236.684,595.801"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MainLoop LE-CheckConfig" id="L-MainLoop-CheckConfig-0" d="M289.92,694.767L328.519,709.439C367.119,724.112,444.317,753.456,482.987,773.228C521.658,793,521.8,803.2,521.871,808.3L521.942,813.401"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CheckConfig LE-Sleep" id="L-CheckConfig-Sleep-0" d="M556.475,951.84L564.524,963.4C572.572,974.96,588.669,998.08,596.717,1020.257C604.766,1042.433,604.766,1063.667,604.766,1083.167C604.766,1102.667,604.766,1120.433,604.766,1145.273C604.766,1170.113,604.766,1202.025,604.766,1235.671C604.766,1269.317,604.766,1304.696,604.766,1331.269C604.766,1357.842,604.766,1375.608,604.766,1391.642C604.766,1407.675,604.766,1421.975,604.766,1436.275C604.766,1450.575,604.766,1464.875,604.766,1479.175C604.766,1493.475,604.766,1507.775,604.766,1533.058C604.766,1558.342,604.766,1594.608,604.766,1632.608C604.766,1670.608,604.766,1710.342,604.766,1740.825C604.766,1771.308,604.766,1792.542,604.766,1813.775C604.766,1835.008,604.766,1856.242,604.766,1875.742C604.766,1895.242,604.766,1913.008,604.766,1929.042C604.766,1945.075,604.766,1959.375,604.766,1973.675C604.766,1987.975,604.766,2002.275,604.766,2016.575C604.766,2030.875,604.766,2045.175,604.766,2061.208C604.766,2077.242,604.766,2095.008,604.766,2112.775C604.766,2130.542,604.766,2148.308,604.766,2164.342C604.766,2180.375,604.766,2194.675,604.766,2208.975C604.766,2223.275,604.766,2237.575,604.766,2253.608C604.766,2269.642,604.766,2287.408,604.766,2305.175C604.766,2322.942,604.766,2340.708,604.766,2356.742C604.766,2372.775,604.766,2387.075,604.766,2401.375C604.766,2415.675,604.766,2429.975,586.728,2442.175C568.691,2454.375,532.616,2464.476,514.579,2469.526L496.541,2474.576"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-Sleep LE-MainLoop" id="L-Sleep-MainLoop-0" d="M411.65,2482.394L358.64,2476.041C305.629,2469.688,199.608,2456.981,146.598,2443.478C93.588,2429.975,93.588,2415.675,93.588,2401.375C93.588,2387.075,93.588,2372.775,93.588,2356.742C93.588,2340.708,93.588,2322.942,93.588,2305.175C93.588,2287.408,93.588,2269.642,93.588,2253.608C93.588,2237.575,93.588,2223.275,93.588,2208.975C93.588,2194.675,93.588,2180.375,93.588,2164.342C93.588,2148.308,93.588,2130.542,93.588,2112.775C93.588,2095.008,93.588,2077.242,93.588,2061.208C93.588,2045.175,93.588,2030.875,93.588,2016.575C93.588,2002.275,93.588,1987.975,93.588,1973.675C93.588,1959.375,93.588,1945.075,93.588,1929.042C93.588,1913.008,93.588,1895.242,93.588,1875.742C93.588,1856.242,93.588,1835.008,93.588,1813.775C93.588,1792.542,93.588,1771.308,93.588,1740.825C93.588,1710.342,93.588,1670.608,93.588,1632.608C93.588,1594.608,93.588,1558.342,93.588,1533.058C93.588,1507.775,93.588,1493.475,93.588,1479.175C93.588,1464.875,93.588,1450.575,93.588,1436.275C93.588,1421.975,93.588,1407.675,93.588,1391.642C93.588,1375.608,93.588,1357.842,93.588,1331.269C93.588,1304.696,93.588,1269.317,93.588,1235.671C93.588,1202.025,93.588,1170.113,93.588,1145.273C93.588,1120.433,93.588,1102.667,93.588,1083.167C93.588,1063.667,93.588,1042.433,93.588,1011.95C93.588,981.467,93.588,941.733,93.588,902C93.588,862.267,93.588,822.533,109.808,790.441C126.028,758.348,158.468,733.896,174.688,721.669L190.908,709.443"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CheckConfig LE-ReloadConfig" id="L-CheckConfig-ReloadConfig-0" d="M494.145,958.429L488.807,968.891C483.468,979.353,472.792,1000.276,467.454,1017.488C462.116,1034.7,462.116,1048.2,462.116,1054.95L462.116,1061.7"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ReloadConfig LE-UpdateLog" id="L-ReloadConfig-UpdateLog-0" d="M462.116,1102.8L462.116,1108.7C462.116,1114.6,462.116,1126.4,462.182,1135.667C462.248,1144.934,462.38,1151.667,462.446,1155.034L462.512,1158.401"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-UpdateLog LE-SetLogLevel" id="L-UpdateLog-SetLogLevel-0" d="M426.919,1269.478L414.764,1281.244C402.609,1293.01,378.3,1316.543,366.145,1333.326C353.991,1350.108,353.991,1360.142,353.991,1365.158L353.991,1370.175"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-UpdateLog LE-NotifyUpdate" id="L-UpdateLog-NotifyUpdate-0" d="M490.546,1277.245L497.349,1287.717C504.152,1298.188,517.759,1319.132,524.562,1338.487C531.366,1357.842,531.366,1375.608,531.366,1391.642C531.366,1407.675,531.366,1421.975,525.391,1432.826C519.416,1443.678,507.466,1451.081,501.491,1454.782L495.516,1458.484"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SetLogLevel LE-NotifyUpdate" id="L-SetLogLevel-NotifyUpdate-0" d="M353.991,1411.275L353.991,1415.442C353.991,1419.608,353.991,1427.942,363.671,1435.949C373.352,1443.957,392.713,1451.639,402.394,1455.479L412.074,1459.32"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-NotifyUpdate LE-SMCheck" id="L-NotifyUpdate-SMCheck-0" d="M462.116,1497.075L462.116,1501.242C462.116,1505.408,462.116,1513.742,462.182,1521.275C462.248,1528.809,462.38,1535.542,462.446,1538.909L462.512,1542.276"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SMCheck LE-SendSignal" id="L-SMCheck-SendSignal-0" d="M424.295,1676.854L413.859,1689.058C403.423,1701.261,382.55,1725.668,372.114,1742.888C361.678,1760.108,361.678,1770.142,361.678,1775.158L361.678,1780.175"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SMCheck LE-Sleep" id="L-SMCheck-Sleep-0" d="M491.06,1686.731L496.444,1697.288C501.828,1707.846,512.597,1728.96,517.981,1750.134C523.366,1771.308,523.366,1792.542,523.366,1813.775C523.366,1835.008,523.366,1856.242,523.366,1875.742C523.366,1895.242,523.366,1913.008,523.366,1929.042C523.366,1945.075,523.366,1959.375,523.366,1973.675C523.366,1987.975,523.366,2002.275,523.366,2016.575C523.366,2030.875,523.366,2045.175,523.366,2061.208C523.366,2077.242,523.366,2095.008,523.366,2112.775C523.366,2130.542,523.366,2148.308,523.366,2164.342C523.366,2180.375,523.366,2194.675,523.366,2208.975C523.366,2223.275,523.366,2237.575,523.366,2253.608C523.366,2269.642,523.366,2287.408,523.366,2305.175C523.366,2322.942,523.366,2340.708,523.366,2356.742C523.366,2372.775,523.366,2387.075,523.366,2401.375C523.366,2415.675,523.366,2429.975,517.148,2440.839C510.931,2451.702,498.496,2459.13,492.279,2462.843L486.061,2466.557"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SendSignal LE-ChildReceive" id="L-SendSignal-ChildReceive-0" d="M361.678,1842.075L361.678,1847.975C361.678,1853.875,361.678,1865.675,361.678,1876.592C361.678,1887.508,361.678,1897.542,361.678,1902.558L361.678,1907.575"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ChildReceive LE-ChildReload" id="L-ChildReceive-ChildReload-0" d="M361.678,1948.675L361.678,1952.842C361.678,1957.008,361.678,1965.342,361.678,1972.792C361.678,1980.242,361.678,1986.808,361.678,1990.092L361.678,1993.375"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ChildReload LE-MPReload" id="L-ChildReload-MPReload-0" d="M361.678,2034.475L361.678,2038.642C361.678,2042.808,361.678,2051.142,361.678,2058.592C361.678,2066.042,361.678,2072.608,361.678,2075.892L361.678,2079.175"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MPReload LE-ClearRules" id="L-MPReload-ClearRules-0" d="M361.678,2141.075L361.678,2145.242C361.678,2149.408,361.678,2157.742,361.678,2165.192C361.678,2172.642,361.678,2179.208,361.678,2182.492L361.678,2185.775"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ClearRules LE-LoadNewRules" id="L-ClearRules-LoadNewRules-0" d="M361.678,2226.875L361.678,2231.042C361.678,2235.208,361.678,2243.542,361.678,2250.992C361.678,2258.442,361.678,2265.008,361.678,2268.292L361.678,2271.575"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-LoadNewRules LE-RulesActive" id="L-LoadNewRules-RulesActive-0" d="M361.678,2333.475L361.678,2337.642C361.678,2341.808,361.678,2350.142,361.678,2357.592C361.678,2365.042,361.678,2371.608,361.678,2374.892L361.678,2378.175"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-RulesActive LE-Sleep" id="L-RulesActive-Sleep-0" d="M361.678,2419.275L361.678,2423.442C361.678,2427.608,361.678,2435.942,369.609,2443.894C377.54,2451.847,393.402,2459.419,401.333,2463.206L409.264,2466.992"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MainLoop LE-MsgProcess" id="L-MainLoop-MsgProcess-0" d="M250.527,734.16L252.33,742.267C254.133,750.374,257.738,766.587,259.541,790.693C261.344,814.8,261.344,846.8,261.344,862.8L261.344,878.8"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MsgProcess LE-UseRules" id="L-MsgProcess-UseRules-0" d="M261.344,919.9L261.344,936.783C261.344,953.667,261.344,987.433,261.344,1009.333C261.344,1031.233,261.344,1041.267,261.344,1046.283L261.344,1051.3"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-UseRules LE-WriteKafka" id="L-UseRules-WriteKafka-0" d="M261.344,1113.2L261.344,1117.367C261.344,1121.533,261.344,1129.867,255.074,1146.221C248.805,1162.575,236.266,1186.95,229.996,1199.137L223.726,1211.325"/><path marker-end="url(#mermaid-svg-1754291466506-cfp84r9cj_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-WriteKafka LE-MainLoop" id="L-WriteKafka-MainLoop-0" d="M202.885,1216.038L196.211,1203.065C189.537,1190.092,176.189,1164.146,169.515,1142.29C162.841,1120.433,162.841,1102.667,162.841,1083.167C162.841,1063.667,162.841,1042.433,162.841,1011.95C162.841,981.467,162.841,941.733,162.841,902C162.841,862.267,162.841,822.533,169.734,792.648C176.628,762.762,190.415,742.724,197.309,732.705L204.203,722.686"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(521.5156326293945, 782.7999982833862)" class="edgeLabel"><g transform="translate(-24.39375114440918, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="48.78750228881836"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">每10秒</span></div></foreignObject></g></g><g transform="translate(604.7656326293945, 1877.475016593933)" class="edgeLabel"><g transform="translate(-8, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="16"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">否</span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(462.1156311035156, 1021.200005531311)" class="edgeLabel"><g transform="translate(-8, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="16"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">是</span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(353.9906311035156, 1340.075011253357)" class="edgeLabel"><g transform="translate(-8, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="16"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">是</span></div></foreignObject></g></g><g transform="translate(531.3656311035156, 1393.3750114440918)" class="edgeLabel"><g transform="translate(-8, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="16"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">否</span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(361.6781311035156, 1750.0750169754028)" class="edgeLabel"><g transform="translate(-24, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="48"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">有更新</span></div></foreignObject></g></g><g transform="translate(523.3656311035156, 2112.7750148773193)" class="edgeLabel"><g transform="translate(-24, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="48"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">无更新</span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(128.64687728881836, 17.899999618530273)" id="flowchart-Start-1468" class="node default startStyle flowchart-label"><rect height="35.80000114440918" width="87.9500002861023" y="-17.90000057220459" x="-43.97500014305115" ry="17.90000057220459" rx="17.90000057220459" style=""/><g transform="translate(-32, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">程序启动</span></div></foreignObject></g></g><g transform="translate(128.64687728881836, 103.69999885559082)" id="flowchart-LoadConfig-1469" class="node default startStyle flowchart-label"><rect height="35.80000114440918" width="111" y="-17.90000057220459" x="-55.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-48, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="96"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">加载配置文件</span></div></foreignObject></g></g><g transform="translate(128.64687728881836, 189.49999809265137)" id="flowchart-StartMain-1471" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="95" y="-17.90000057220459" x="-47.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-40, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="80"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">启动主进程</span></div></foreignObject></g></g><g transform="translate(128.64687728881836, 275.2999973297119)" id="flowchart-StartSM-1473" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="127" y="-17.90000057220459" x="-63.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">启动服务管理器</span></div></foreignObject></g></g><g transform="translate(69.39375305175781, 361.09999656677246)" id="flowchart-StartTCP-1475" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="138.7874984741211" y="-17.90000057220459" x="-69.39374923706055" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-61.89374923706055, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="123.7874984741211"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">启动TCP监听线程</span></div></foreignObject></g></g><g transform="translate(236.28750610351562, 361.09999656677246)" id="flowchart-StartChild-1477" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="95" y="-17.90000057220459" x="-47.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-40, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="80"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">启动子进程</span></div></foreignObject></g></g><g transform="translate(236.28750610351562, 446.899995803833)" id="flowchart-StartMP-1479" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="127" y="-17.90000057220459" x="-63.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">启动消息处理器</span></div></foreignObject></g></g><g transform="translate(236.28750610351562, 532.6999950408936)" id="flowchart-StartKW-1481" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="135.01250457763672" y="-17.90000057220459" x="-67.50625228881836" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-60.00625228881836, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="120.01250457763672"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">启动Kafka写入器</span></div></foreignObject></g></g><g transform="translate(236.28750610351562, 673.9999961853027)" id="flowchart-MainLoop-1483" class="node default decisionStyle flowchart-label"><polygon style="" transform="translate(-73.40000057220459,73.40000057220459)" class="label-container" points="73.40000057220459,0 146.80000114440918,-73.40000057220459 73.40000057220459,-146.80000114440918 0,-73.40000057220459"/><g transform="translate(-48, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="96"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">主循环运行中</span></div></foreignObject></g></g><g transform="translate(521.5156326293945, 902.0000019073486)" id="flowchart-CheckConfig-1485" class="node default decisionStyle flowchart-label"><polygon style="" transform="translate(-83.80000114440918,83.80000114440918)" class="label-container" points="83.80000114440918,0 167.60000228881836,-83.80000114440918 83.80000114440918,-167.60000228881836 0,-83.80000114440918"/><g transform="translate(-48, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="96"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">检查配置文件<br />是否更新?</span></div></foreignObject></g></g><g transform="translate(451.5437545776367, 2487.1750106811523)" id="flowchart-Sleep-1487" class="node default endStyle flowchart-label"><rect height="35.80000114440918" width="79.7874984741211" y="-17.90000057220459" x="-39.89374923706055" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.39374923706055, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="64.7874984741211"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">睡眠10秒</span></div></foreignObject></g></g><g transform="translate(462.1156311035156, 1084.9000053405762)" id="flowchart-ReloadConfig-1491" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="159" y="-17.90000057220459" x="-79.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-72, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="144"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">重新加载配置到内存</span></div></foreignObject></g></g><g transform="translate(462.1156311035156, 1233.9375076293945)" id="flowchart-UpdateLog-1493" class="node default decisionStyle flowchart-label"><polygon style="" transform="translate(-70.73750114440918,70.73750114440918)" class="label-container" points="70.73750114440918,0 141.47500228881836,-70.73750114440918 70.73750114440918,-141.47500228881836 0,-70.73750114440918"/><g transform="translate(-34.9375, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="69.875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">日志级别<br />是否变更?</span></div></foreignObject></g></g><g transform="translate(353.9906311035156, 1393.3750114440918)" id="flowchart-SetLogLevel-1495" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="143" y="-17.90000057220459" x="-71.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-64, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="128"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">设置新的日志级别</span></div></foreignObject></g></g><g transform="translate(462.1156311035156, 1479.1750106811523)" id="flowchart-NotifyUpdate-1497" class="node default updateStyle flowchart-label"><rect height="35.80000114440918" width="143" y="-17.90000057220459" x="-71.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-64, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="128"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">设置配置更新标志</span></div></foreignObject></g></g><g transform="translate(462.1156311035156, 1630.8750133514404)" id="flowchart-SMCheck-1501" class="node default decisionStyle flowchart-label"><polygon style="" transform="translate(-83.80000114440918,83.80000114440918)" class="label-container" points="83.80000114440918,0 167.60000228881836,-83.80000114440918 83.80000114440918,-167.60000228881836 0,-83.80000114440918"/><g transform="translate(-48, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="96"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">服务管理器<br />检查更新标志</span></div></foreignObject></g></g><g transform="translate(361.6781311035156, 1813.775016784668)" id="flowchart-SendSignal-1503" class="node default processStyle flowchart-label"><rect height="56.60000228881836" width="164.4875030517578" y="-28.30000114440918" x="-82.2437515258789" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-74.7437515258789, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="149.4875030517578"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">向所有子进程发送<br />CONFIG_UPDATE信号</span></div></foreignObject></g></g><g transform="translate(361.6781311035156, 1930.775016784668)" id="flowchart-ChildReceive-1507" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="127" y="-17.90000057220459" x="-63.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">子进程接收信号</span></div></foreignObject></g></g><g transform="translate(361.6781311035156, 2016.5750160217285)" id="flowchart-ChildReload-1509" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="159" y="-17.90000057220459" x="-79.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-72, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="144"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">子进程重新加载配置</span></div></foreignObject></g></g><g transform="translate(361.6781311035156, 2112.7750148773193)" id="flowchart-MPReload-1511" class="node default processStyle flowchart-label"><rect height="56.60000228881836" width="127" y="-28.30000114440918" x="-63.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">通知消息处理器<br />重新加载规则</span></div></foreignObject></g></g><g transform="translate(361.6781311035156, 2208.97501373291)" id="flowchart-ClearRules-1513" class="node default updateStyle flowchart-label"><rect height="35.80000114440918" width="111" y="-17.90000057220459" x="-55.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-48, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="96"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">清空现有规则</span></div></foreignObject></g></g><g transform="translate(361.6781311035156, 2305.175012588501)" id="flowchart-LoadNewRules-1515" class="node default updateStyle flowchart-label"><rect height="56.60000228881836" width="173.75" y="-28.30000114440918" x="-86.875" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-79.375, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="158.75"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">加载新的<br />direct_kafka_rule规则</span></div></foreignObject></g></g><g transform="translate(361.6781311035156, 2401.375011444092)" id="flowchart-RulesActive-1517" class="node default updateStyle flowchart-label"><rect height="35.80000114440918" width="127" y="-17.90000057220459" x="-63.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">新规则立即生效</span></div></foreignObject></g></g><g transform="translate(261.34375381469727, 902.0000019073486)" id="flowchart-MsgProcess-1521" class="node default endStyle flowchart-label"><rect height="35.80000114440918" width="127" y="-17.90000057220459" x="-63.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">处理客户端消息</span></div></foreignObject></g></g><g transform="translate(261.34375381469727, 1084.9000053405762)" id="flowchart-UseRules-1523" class="node default endStyle flowchart-label"><rect height="56.60000228881836" width="127" y="-28.30000114440918" x="-63.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">使用最新的规则<br />进行消息路由</span></div></foreignObject></g></g><g transform="translate(212.09375381469727, 1233.9375076293945)" id="flowchart-WriteKafka-1525" class="node default endStyle flowchart-label"><rect height="35.80000114440918" width="167.0124969482422" y="-17.90000057220459" x="-83.5062484741211" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-76.0062484741211, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="152.0124969482422"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">写入对应的Kafka主题</span></div></foreignObject></g></g></g></g></g></svg>