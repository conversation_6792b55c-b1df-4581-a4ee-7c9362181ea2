graph TB
    %% 外部客户端
    subgraph "客户端"
        C1[Client 1]
        C2[Client 2]
        C3[Client N]
    end

    %% 主进程
    subgraph "主进程 (metricsToKafka.py)"
        MP[主进程]
        CFG[配置管理<br/>DCConfig]
        LOG[日志系统<br/>logging2]
        SIG[信号处理<br/>SIGINT/SIGTERM]
        
        MP --> CFG
        MP --> LOG
        MP --> SIG
    end

    %% 服务管理器
    subgraph "服务管理器 (MetricsServerManager)"
        SM[服务管理器线程]
        
        subgraph "TCP监听线程池"
            T1[TCP监听线程1<br/>端口8686]
            T2[TCP监听线程2<br/>端口8688]
        end
        
        subgraph "管道通信"
            P1[Pipe1]
            P2[Pipe2]
            PN[PipeN]
        end
        
        SM --> T1
        SM --> T2
        SM --> P1
        SM --> P2
        SM --> PN
    end

    %% 子进程
    subgraph "子进程1 (MetricsConnectionManager)"
        CP1[子进程1]
        
        subgraph "消息处理线程池1"
            MP1[消息处理线程1]
            MP2[消息处理线程2]
            MP3[消息处理线程N]
        end
        
        subgraph "Kafka写入线程池1"
            KW1[Kafka写入线程1]
            KW2[Kafka写入线程2]
            KW3[Kafka写入线程N]
        end
        
        subgraph "队列1"
            MQ1[消息队列]
            KQ1[Kafka队列]
        end
        
        CP1 --> MP1
        CP1 --> MP2
        CP1 --> MP3
        CP1 --> KW1
        CP1 --> KW2
        CP1 --> KW3
        MP1 --> MQ1
        MP2 --> MQ1
        MP3 --> MQ1
        MQ1 --> KQ1
        KQ1 --> KW1
        KQ1 --> KW2
        KQ1 --> KW3
    end

    %% 子进程2
    subgraph "子进程2 (MetricsConnectionManager)"
        CP2[子进程2]
        
        subgraph "消息处理线程池2"
            MP4[消息处理线程1]
            MP5[消息处理线程2]
        end
        
        subgraph "Kafka写入线程池2"
            KW4[Kafka写入线程1]
            KW5[Kafka写入线程2]
        end
        
        subgraph "队列2"
            MQ2[消息队列]
            KQ2[Kafka队列]
        end
        
        CP2 --> MP4
        CP2 --> MP5
        CP2 --> KW4
        CP2 --> KW5
        MP4 --> MQ2
        MP5 --> MQ2
        MQ2 --> KQ2
        KQ2 --> KW4
        KQ2 --> KW5
    end

    %% Kafka集群
    subgraph "Kafka集群"
        K1[Topic: chfp_topic]
        K2[Topic: hostAlive_topic-1]
        K3[Topic: hostAlive_topic-2]
    end

    %% 配置文件
    subgraph "配置文件"
        XML[metricsToKafka.cfg.xml<br/>- direct_kafka_rule<br/>- server配置<br/>- kafka配置]
    end

    %% 连接关系
    C1 -.->|TCP连接| T1
    C2 -.->|TCP连接| T1
    C3 -.->|TCP连接| T2

    T1 -->|负载均衡| P1
    T1 -->|负载均衡| P2
    T2 -->|负载均衡| P1
    T2 -->|负载均衡| P2

    P1 -->|传递连接| CP1
    P2 -->|传递连接| CP2

    KW1 -.->|写入| K1
    KW2 -.->|写入| K2
    KW3 -.->|写入| K3
    KW4 -.->|写入| K1
    KW5 -.->|写入| K2

    MP --> SM
    XML -.->|热更新| CFG
    CFG -.->|配置变更通知| SM
    SM -.->|CONFIG_UPDATE信号| P1
    SM -.->|CONFIG_UPDATE信号| P2

    %% 样式
    classDef processStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef threadStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef queueStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef kafkaStyle fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef configStyle fill:#fff8e1,stroke:#f57c00,stroke-width:2px

    class MP,SM,CP1,CP2 processStyle
    class T1,T2,MP1,MP2,MP3,MP4,MP5,KW1,KW2,KW3,KW4,KW5 threadStyle
    class MQ1,MQ2,KQ1,KQ2,P1,P2,PN queueStyle
    class K1,K2,K3 kafkaStyle
    class CFG,LOG,XML configStyle