#!/usr/bin/python
# -*- coding: utf-8 -*-
"""
metricsToKafka 简单功能测试
"""

import socket
import json
import time
import threading

def test_tcp_connection():
    """测试TCP连接功能"""
    print("测试TCP连接...")
    
    try:
        # 连接到metricsToKafka服务
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('**************', 8686))
        
        # 测试消息1
        test_message1 = {
            "model": "chfp",
            "body": "test_data_1|timestamp_1|value_1"
        }
        
        sock.send(json.dumps(test_message1).encode('utf-8'))
        print(f"发送测试消息1: {test_message1}")
        time.sleep(1)
        
        # 测试消息2  
        test_message2 = {
            "model": "openapi-1",
            "body": "test_data_2|timestamp_2|value_2"
        }
        
        sock.send(json.dumps(test_message2).encode('utf-8'))
        print(f"发送测试消息2: {test_message2}")
        time.sleep(1)
        
        # 测试无效消息格式
        invalid_message = {
            "invalid_key": "test"
        }
        
        sock.send(json.dumps(invalid_message).encode('utf-8'))
        print(f"发送无效消息: {invalid_message}")
        time.sleep(1)
        
        # 测试不存在的model
        unknown_model_message = {
            "model": "unknown_model",
            "body": "test_data_3"
        }
        
        sock.send(json.dumps(unknown_model_message).encode('utf-8'))
        print(f"发送未知model消息: {unknown_model_message}")
        time.sleep(1)
        
        sock.close()
        print("TCP连接测试完成")
        
    except Exception as e:
        print(f"TCP连接测试失败: {e}")

def test_multiple_connections():
    """测试多连接并发"""
    print("测试多连接并发...")
    
    def send_messages(connection_id):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(('**************', 8686))
            
            for i in range(5):
                message = {
                    "model": "chfp",
                    "body": f"conn_{connection_id}_msg_{i}|{time.time()}"
                }
                sock.send(json.dumps(message).encode('utf-8'))
                print(f"连接{connection_id} 发送消息{i}: {message}")
                time.sleep(0.5)
                
            sock.close()
            print(f"连接{connection_id} 测试完成")
            
        except Exception as e:
            print(f"连接{connection_id} 测试失败: {e}")
    
    # 创建多个并发连接
    threads = []
    for i in range(3):
        thread = threading.Thread(target=send_messages, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print("多连接并发测试完成")

def test_different_ports():
    """测试不同端口"""
    print("测试不同端口...")
    
    ports = [8686, 8688]
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(('**************', port))
            
            message = {
                "model": "openapi-2", 
                "body": f"port_{port}_test|{time.time()}"
            }
            
            sock.send(json.dumps(message).encode('utf-8'))
            print(f"端口{port} 发送消息: {message}")
            
            sock.close()
            print(f"端口{port} 测试成功")
            
        except Exception as e:
            print(f"端口{port} 测试失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("metricsToKafka 功能测试")
    print("=" * 50)
    print()
    print("注意: 请确保metricsToKafka服务已启动")
    print("启动命令: cd src && python3 metricsToKafka.py ../cfg/metricsToKafka.cfg.xml")
    print()
    
    input("按回车键开始测试...")
    
    # 基本连接测试
    test_tcp_connection()
    print()
    
    # 多连接测试
    test_multiple_connections()
    print()
    
    # 不同端口测试
    test_different_ports()
    print()
    
    print("=" * 50)
    print("测试完成！请检查程序日志确认消息处理情况")
    print("=" * 50)