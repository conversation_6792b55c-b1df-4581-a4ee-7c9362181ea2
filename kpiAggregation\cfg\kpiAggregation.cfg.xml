<kpiAggregation>
    <log><!--修改需要重启程序,loglevel除外-->
        <param name="logpath">/codebill/LN/src_5g/tools/kpiAggregation/log</param>
        <param name="loglevel">DEBUG</param><!--DEBUG, INFO, WARNING, ERROR-->    
    </log>
    <common><!--修改需要重启程序-->
        <param name="workPidNums">1</param><!--处理任务的子进程数，默认值1-->
        <param name="deal_thread_num">1</param><!--消息解析线程-->
        <param name="out_thread_num">4</param><!-- 处写kafka的线程数，默认值1 -->
        <param name="queue_size">1000</param><!-- 队列大小 -->
        <param name="sqlConfig">/codebill/LN/src_5g/tools/kpiAggregation/cfg/kpiAggregation.sql.xml</param><!-- sql文件明，必须是绝对路径或者相对路径 -->
        <param name="cycleNums">KpiCycle|30</param><!-- ops_sys_kpi_detail表KpiCycle的个数,默认30 -->
        <param name="stateNums">KpiState|7</param><!-- ops_sys_kpi_detail表KpiState的个数,默认7 -->
        <param name="comModule">common</param><!-- ops_sys_kpi_detail_def表公共模块名,默认common -->
        <param name="separator">^</param><!-- 写kafka的分隔符，默认是^ -->
    </common>
    <kafka><!--修改需要重启程序-->
        <param name="safe_cert">0</param><!--0-不认证，1-认证-->
        <param name="bootstrap_servers">**************:9092</param>
        <param name="security_protocol">SASL_PLAINTEXT</param><!--自定义-->
        <param name="sasl_mechanism">PLAIN</param><!--自定义-->
        <param name="sasl_plain_username">itete</param><!--自定义-->
        <param name="sasl_plain_password">xH9%iO2*tK23aR9f</param><!--自定义-->
    </kafka>
    <server><!--修改需要重启程序-->
        <group1>
            <param name="server_ip">**************</param>
            <param name="server_port">8686</param>
        </group1>

        <group2>
            <param name="server_ip">**************</param>
            <param name="server_port">8688</param>
        </group2>
    </server>

    <mes_rule><!--修改不需要重启程序-->
        <group1>
            <param name='rule_name'>default</param><!--配置规则增加一个default规则，如果收到的json消息的module在规则里面查不到，则查询指标定义表是否有定义，如果有定义，则使用默认规则-->
            <param name='mes_input'>{"batid":"","cluster":"","data":[{"Host":"","TraceKpi":{"*":""},"pid":"","sub_cluster":"","type":""}],"module":"","subsys": "","system":""}</param><!--json消息格式-->
            <param name='merge_key'>system|subsys|module|batid|Host</param><!--指标合并的key-->
            <param name='kafka_topic'>DICDB_topic</param><!--kafka的topic名-->
            <param name='merge_time'>30</param><!--合并的维度，单位s，0表示不合并-->
            <param name='merge_latency'>10</param><!--合并时延,在内存中多停留的时间,单位s-->
        </group1>
        <group2>
            <param name='rule_name'>chfproxy,re</param><!--多个用,分割,配置不支持动态加载-->
            <param name='mes_input'>{"rule_name":"","Module":"","kpiType":"","kpiTime":"","kpiLst":[{"==kpiName==":"==kpiValue==","BATCH":""}]}</param>
            <param name='mes_output'>{"Module":"","kpiType":"","kpiTime":"","==kpiName==":"","BATCH":"","==kpiValue==":""}</param>
            <param name='merge_key'>Module|==kpiName==|BATCH</param>
            <param name='merge_value'>==kpiValue==</param>
            <param name='kafka_topic'>chfproxy_topic</param>
            <param name='merge_time'>3</param><!--单位s-->
        </group2>
        <group3>
            <param name='rule_name'>msgexch</param><!--多个用,分割,配置不支持动态加载-->
            <param name='mes_input'>{"rule_name":"","Module":"","kpiType":"","kpiTime":"","kpiLst":[{"==kpiName==":"==kpiValue==","BATCH":""}]}</param>
            <param name='mes_output'>{"Module":"","kpiType":"","kpiTime":"","==kpiName==":"","BATCH":"","==kpiValue==":""}</param>
            <param name='merge_key'>Module|==kpiName==|BATCH</param>
            <param name='merge_value'>==kpiValue==</param>
            <param name='kafka_topic'>msgexch_topic</param>
            <param name='merge_time'>3</param>
        </group3>
        <group4>
            <param name='rule_name'>msgexch</param><!--多个用,分割,配置不支持动态加载-->
            <param name='mes_input'>{"system":"","subsys":"","module":"","batid":"","cluster": "","data":[{"Host":"","pid":"","latnid":"","batId":"","TraceKpi":{}}]}</param><!--json消息格式-->
            <param name='merge_key'>system|subsys|module|batId|pid|latnid</param><!--指标合并的key-->
            <param name='kafka_topic'>msgexch_topic</param><!--kafka的topic名-->
            <param name='merge_time'>3</param><!--合并的维度，单位s，0表示不合并-->
            <param name='merge_latency'>10</param><!--合并时延,在内存中多停留的时间,单位s-->
        </group4>
        <group5>
            <param name='rule_name'>CHF</param><!--多个用,分割,配置不支持动态加载-->
            <param name='mes_input'>{"system":"","subsys":"","module":"","cluster": "","batid":"","data":[{"host":"","pid":"","latnid":"","type":"","batId":"","batSec":"","TraceKpi":{"*":""}}]}</param><!--json消息格式-->
            <param name='merge_key'>system|subsys|module|batId|host|pid|latnid</param><!--指标合并的key-->
            <param name='kafka_topic'>chf_topic</param><!--kafka的topic名-->
            <param name='merge_time'>0</param><!--合并的维度，单位s，0表示不合并-->
            <param name='merge_latency'>10</param><!--合并时延,在内存中多停留的时间,单位s-->
        </group5>
        <group6>
            <param name='rule_name'>DICDB</param><!--多个用,分割,配置不支持动态加载-->
            <param name='mes_input'>{"batid":"","cluster":"","data":[{"Host":"","TraceKpi":{"*":""},"pid":"","sub_cluster":"","type":""}],"module":"","subsys": "","system":""}</param><!--json消息格式-->
            <param name='merge_key'>system|subsys|module|batid|Host</param><!--指标合并的key-->
            <param name='kafka_topic'>DICDB_topic</param><!--kafka的topic名-->
            <param name='merge_time'>30</param><!--合并的维度，单位s，0表示不合并-->
            <param name='merge_latency'>10</param><!--合并时延,在内存中多停留的时间,单位s-->
        </group6>
        <group7>
            <param name='rule_name'>mqback</param><!--多个用,分割,配置不支持动态加载-->
            <param name='mes_input'>{"system": "", "subsys": "", "module": "", "data": [{"host": "", "pid": "", "latnid": "", "batId": "", "TraceKpi": {"*": ""}}]}</param><!--json消息格式-->
            <param name='merge_key'>system|subsys|module|batid|Host</param><!--指标合并的key-->
            <param name='kafka_topic'>mqback_topic</param><!--kafka的topic名-->
            <param name='merge_time'>0</param><!--合并的维度，单位s，0表示不合并-->
            <param name='merge_latency'>10</param><!--合并时延,在内存中多停留的时间,单位s-->
        </group7>
        <group8>
            <param name='rule_name'>hostAlive</param><!--多个用,分割,配置不支持动态加载-->
            <param name='mes_input'>{"system": "", "subsys": "", "module": "", "data": [{"host": "", "pid": "", "latnid": "", "batId": "", "TraceKpi": {"*": ""}}]}</param><!--json消息格式-->
            <param name='merge_key'>system|subsys|module|batid|Host</param><!--指标合并的key-->
            <param name='kafka_topic'>mqback_topic</param><!--kafka的topic名-->
            <param name='merge_time'>0</param><!--合并的维度，单位s，0表示不合并-->
            <param name='merge_latency'>10</param><!--合并时延,在内存中多停留的时间,单位s-->
        </group8>
    </mes_rule>
</kpiAggregation>