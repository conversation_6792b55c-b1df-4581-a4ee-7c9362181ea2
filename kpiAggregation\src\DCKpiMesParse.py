#!/usr/bin/python
# -*- coding: utf-8 -*-
#指标消息的解析

import queue
import threading
import logging2
import time
import traceback
import json
import datetime
# from DCKpiMesTrans import DCKpiMesTrans

class DCKpiMesParse(threading.Thread):
    def __init__(self, isRun, cfgObj, taskQueue, msgTransList, dbLoad):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_cfgObj = cfgObj
        self.m_taskQueue = taskQueue
        self.m_msgTransList = msgTransList
        self.m_dbLoad = dbLoad
        self.m_transThreads = int( self.m_cfgObj.get_single_value('common', 'deal_thread_num') )
        pass

    def setRunState(self, isRun):
        self.m_isRun = isRun

    def compare_dict(self, d1, d2):
        try:
            #{'*':""}这种格式的模板对象直接通过
            if len(d1.keys()) == 1 and next(iter(d1.keys())) == '*':
                return True
            if d1.keys() != d2.keys():
                return False
            for key in d1:
                if isinstance(d1[key], dict):
                    if not isinstance(d2[key], dict):
                        return False
                    if key == '*':
                        continue
                    if not self.compare_dict(d1[key], d2[key]):
                        return False
                elif isinstance(d1[key], list):
                    if not isinstance(d2[key], list):
                        return False  
                    if not self.compare_list(d1[key], d2[key]):
                        return False
                else:                
                    # 比较值（假设不需要值相等）
                    pass
            return True
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return False

    def compare_list(self, l1, l2):
        try:
            if  len(l2) < len(l1):
                return False
            for item2 in l2:
                for item1 in l1:
                    if isinstance(item1, dict):
                        if not isinstance(item2, dict):
                            return False
                        if not self.compare_dict(item1, item2):
                            return False
                    else:
                        # 比较非字典类型的列表项（假设不需要值相等）
                        pass
            return True
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return False
        
    #校验json是否存在必备字段
    def checkJson(self, jsonData, cfgModule):
        try:
            exampleJsonData = json.loads(cfgModule['mes_input'])
            return self.compare_dict(exampleJsonData, jsonData)
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return False

    def timeStampChange(self, timeStp, mergeTime):
        try:
            if ( int(mergeTime) <= 0 ):
                return timeStp
            if len(timeStp) == 14:
                dt = datetime.datetime.strptime(timeStp, '%Y%m%d%H%M%S')
            elif len(timeStp) == 12:  # YYYYMMDDHHMM
                dt = datetime.datetime.strptime(timeStp, '%Y%m%d%H%M')
            else:
                logging2.error("time stamp[%s] length error"%(timeStp))
                return ""
            pro_timestamp = (int(dt.timestamp()) // int(mergeTime)) * int(mergeTime)
            # 将处理后的时间戳转换回时间
            processed_time = time.strftime('%Y%m%d%H%M%S', time.localtime(pro_timestamp))
            return processed_time
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return ""
    
    #消息合并
    def mesMerge(self, jsonData, cfgModule):
        try:
            #1. 加载数据库，做数据合并; 数据写入内存
            module_Name = jsonData['module']
            moduleDef = self.m_dbLoad.getModuleData(module_Name.lower())
            mergeKey = cfgModule['merge_key']
            mergeKeyList = mergeKey.split('|')

            dataDict = {key.lower(): value for key, value in jsonData.items() if not isinstance(value, (dict, list))}

            for key, value in jsonData.items():
                if isinstance(value, dict):
                    logging2.error("the msg format not support multi-level! msg:%s"%(jsonData))
                    return -1
                elif isinstance(value, list):
                    for item in value:
                        if not isinstance(item, dict):
                            logging2.error("expected a dict, but got %s, msg:%s", type(item).__name__, jsonData)
                            return -1
                        childData = {}
                        childData = dataDict.copy()
                        pid_list = []
                        # logging2.debug("item:%s, childData:%s, dataDict:%s"%(item.items(), childData, dataDict))
                        for ChildKey, ChildValue in item.items():
                            if isinstance(ChildValue, dict):
                                for ChildKey1,ChildValue1 in ChildValue.items():
                                    logging2.debug(f"ChildKey1:%s"%(ChildKey1))
                                    if isinstance(ChildValue1, dict):
                                        valStrList = [f"{k}:{v};" for k, v in ChildValue1.items()]
                                        valSum = sum(int(v) for v in ChildValue1.values())
                                        childData[ChildKey1.lower()] = f"{valSum}|{''.join(valStrList)}"
                                    else:
                                        childData[ChildKey1.lower()] = ChildValue1
                            else:
                                if ChildKey.lower() == 'host':#"host": "**************:"去掉首尾的:
                                    ChildValue = ChildValue.strip(':')
                                elif ChildKey.lower() == 'pid':#"pid": "12345 12345"pid对应多个进程号
                                    pid_list = ChildValue.split()
                                childData[ChildKey.lower()] = ChildValue
                                
                        if len(pid_list) == 0:#pid不存在，直接写一条数据
                            pid_list.append("")
                        
                        addMulThread = False                           
                        for pid in pid_list:#pid存在一个或者多个，若合并维度包含pid,需要写多个任务，否则一条处
                            dataKeyParts = []
                            for merItem in mergeKeyList:
                                merItem = merItem.lower()
                                if(merItem == 'batid'):
                                    outBatchId = self.timeStampChange(childData[merItem], cfgModule['merge_time'])
                                    if not outBatchId:
                                        logging2.error("timeStampChange[%s] error..,msg:%s"%(childData[merItem], jsonData))
                                        return -1
                                    dataKeyParts.append(str(outBatchId))
                                    childData[merItem] = outBatchId
                                elif(merItem == 'pid'):#合并维度存在pid处理
                                    dataKeyParts.append(str(pid))
                                    childData[merItem] = pid
                                    addMulThread = True
                                else:
                                    dataKeyParts.append(str(childData[merItem]))
                            dataKey = "|".join(dataKeyParts)
                            threadIdx = sum(ord(char) for char in dataKey) % self.m_transThreads
                            mergeDimen = int(cfgModule.get('merge_latency', 0))
                            timeDimen = int(cfgModule.get("merge_time", 0)) + mergeDimen

                            # logging2.debug("childData:%s"%(childData))
                            for childDataItem,childDataValue in childData.items():
                                if moduleDef is not None and childDataItem in moduleDef:
                                    dataList = moduleDef[childDataItem]
                                    # logging2.debug("childDataItem:%s, dataList[1]:%s"%(childDataItem, dataList[1]))
                                    if int(dataList[1]) == 1:#kpi_type(指标类型:1cycle,2state最新值,0common,3state平均值)
                                        self.m_msgTransList[threadIdx].addDataCycle(dataKey, childDataItem, childDataValue, timeDimen)    
                                    elif int(dataList[1]) == 3:
                                        self.m_msgTransList[threadIdx].addDataStateAvg(dataKey, childDataItem, childDataValue, timeDimen)
                                    else:
                                        self.m_msgTransList[threadIdx].addDataState(dataKey, childDataItem, childDataValue, timeDimen, childData.get('batid', None))
                                else:
                                    self.m_msgTransList[threadIdx].addDataState(dataKey, childDataItem, childDataValue, timeDimen, childData.get('batid', None))
                            
                            if not addMulThread:
                                break
                else:
                    pass
        except KeyError as e:
            logging2.error(f"Missing key in jsonData: {e}")
        except ValueError as e:
            logging2.error(f"Value error: {e}")           
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))

    def mesUnMerge(self, jsonData, cfgModule):
        #1.不管是否合并，都取合并维度进行数据路由
        try:
            mergeKey = cfgModule['merge_key']
            mergeKeyList = mergeKey.split('|')

            dataDict = {key.lower(): value for key, value in jsonData.items() if not isinstance(value, (dict, list))}

            for key, value in jsonData.items():
                if isinstance(value, dict):
                    logging2.error("the msg format not support multi-level! msg:%s"%(jsonData))
                    return -1
                elif isinstance(value, list):
                    for item in value:
                        if not isinstance(item, dict):
                            logging2.error("expected a dict, but got %s, msg:%s", type(item).__name__, jsonData)
                            return -1
                        childData = {}#key不区分大小写
                        childData = dataDict.copy()
                        pid_list = []
                        for ChildKey, ChildValue in item.items():
                            logging2.debug(f"ChildKey:%s"%(ChildKey))
                            if isinstance(ChildValue, dict):
                                for ChildKey1,ChildValue1 in ChildValue.items():
                                    logging2.debug(f"ChildKey1:%s"%(ChildKey1))
                                    if isinstance(ChildValue1, dict):
                                        valStrList = [f"{k}:{v};" for k, v in ChildValue1.items()]
                                        valSum = sum(int(v) for v in ChildValue1.values())
                                        childData[ChildKey1.lower()] = f"{valSum}|{''.join(valStrList)}"
                                    else:
                                        childData[ChildKey1.lower()] = ChildValue1
                            else:
                                if ChildKey.lower() == 'host':#"host": "**************:"去掉首尾的:
                                    ChildValue = ChildValue.strip(':')
                                elif ChildKey.lower() == 'pid':#"pid": "12345 12345"pid对应多个进程号
                                    pid_list = ChildValue.split()
                                childData[ChildKey.lower()] = ChildValue

                        if len(pid_list) == 0:#pid不存在，直接写一条数据
                            pid_list.append("")

                        addMulThread = False
                        for pid in pid_list:#pid存在一个或者多个，若合并维度包含pid,需要写多个任务，否则一条处理
                            dataKeyParts = []
                            for merItem in mergeKeyList:
                                merItem = merItem.lower()
                                if(merItem == 'batid'):
                                    outBatchId = self.timeStampChange(childData[merItem], cfgModule['merge_time'])
                                    if not outBatchId:
                                        logging2.error("timeStampChange[%s] error..,msg:%s"%(childData[merItem], jsonData))
                                        return -1
                                    dataKeyParts.append(str(outBatchId))
                                elif(merItem == 'pid'):#合并维度存在pid处理
                                    dataKeyParts.append(str(pid))
                                    childData[merItem] = pid
                                    addMulThread = True
                                else:
                                    dataKeyParts.append(str(childData[merItem]))
                            dataKey = "|".join(dataKeyParts)
                            threadIdx = sum(ord(char) for char in dataKey) % self.m_transThreads
                            # mergeDimen = int(cfgModule.get('merge_latency', 0))
                            timeDimen = int(cfgModule.get("merge_time", 0))#不合并不需要加merge_latency 
                            mergeDimen = int(cfgModule.get('merge_latency', 0))
                            timeDimen = min(timeDimen + mergeDimen, 1)#增加1s延迟，防止一条消息没处理完就输出

                            logging2.debug(f"dataKey:%s, timeDimen:%s, data:%s"%(dataKey, timeDimen, childData))
                            self.m_msgTransList[threadIdx].addData(dataKey, childData, timeDimen)
                            if not addMulThread:
                                break
                else:
                    pass
        except KeyError as e:
            logging2.error(f"Missing key in jsonData: {e}")
        except ValueError as e:
            logging2.error(f"Value error: {e}")
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))

    #消息写入内存
    def mesTrans(self, jsonData, cfgModule):
        try:     
            #判断数据是否合并   
            if( int(cfgModule['merge_time']) <= 0):
                self.mesUnMerge( jsonData, cfgModule )
            else:
                self.mesMerge( jsonData, cfgModule )   

            return True
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return False

    def run(self):
        while True:
            if not self.m_isRun:
                logging2.warn("DCKpiMesParse thread exit")
                break
            data = None
            try:
                #设置为非阻塞模式
                if self.m_taskQueue.empty():
                    time.sleep(0.5)
                    continue

                data = self.m_taskQueue.get(timeout=1)
                if data is None:
                    time.sleep(0.5)
                    continue
                # logging2.debug("data[%s]"%(data))
                jsonStr = data.decode('utf-8')
                logging2.debug("jsonStr[%s]"%(jsonStr))
                #按照模板解析数据
                jsonData = json.loads(jsonStr)               
                #获取模块名，默认第一个字段的值
                module_Name = ''
                if 'module' in jsonData:
                    module_Name = jsonData['module']
                else:
                    logging2.error(f"JSON data does not contain 'module' key: {jsonData}")
                    continue

                cfgModule = self.m_cfgObj.get_group_value('mes_rule', module_Name)
                if not cfgModule:
                    logging2.error("the module[%s] not exist in config file!"%(module_Name))
                    # 判断是否在指标表里有，如果有则加载默认规则，数据加载都转成了小写字母
                    moduleDef = self.m_dbLoad.getModuleData(module_Name.lower())
                    if moduleDef == None:
                        logging2.error("the module[%s] not exist in db!"%(module_Name))
                        continue
                    else:
                        logging2.info("the module[%s] not exist in config file, but exist in db, use default rule!"%(module_Name))
                        cfgModule = self.m_cfgObj.get_group_value('mes_rule', 'default')
                        if not cfgModule:
                            logging2.error("the default module not exist in config file!")
                            continue

                #校验json是否存在必备字段
                if( not self.checkJson(jsonData, cfgModule) ):
                    logging2.error("the message format and template do not match! msg:%s, module:%s"%(jsonData, module_Name))
                    continue
                
                #数据合并放入内存
                self.mesTrans(jsonData, cfgModule)
            except queue.Empty:
                time.sleep(0.5)                
            except json.JSONDecodeError as e:
                logging2.error("Failed to decode errorinfo:%s, data:%s"%(e, data))
            except Exception as err:
                stack_trace = traceback.format_exc()
                logging2.error(stack_trace)
                traceback.print_exc()
                logging2.error(str(err))