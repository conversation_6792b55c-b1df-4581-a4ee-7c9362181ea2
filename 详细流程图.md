flowchart TD
    Start([程序启动]) --> LoadConfig[加载配置文件]
    LoadConfig --> StartMain[启动主进程]
    StartMain --> StartSM[启动服务管理器]
    StartSM --> StartTCP[启动TCP监听线程]
    StartSM --> StartChild[启动子进程]
    StartChild --> StartMP[启动消息处理器]
    StartMP --> StartKW[启动Kafka写入器]
    
    StartKW --> MainLoop{主循环运行中}
    
    %% 配置检查循环
    MainLoop -->|每10秒| CheckConfig{检查配置文件<br/>是否更新?}
    CheckConfig -->|否| Sleep[睡眠10秒]
    Sleep --> MainLoop
    
    CheckConfig -->|是| ReloadConfig[重新加载配置到内存]
    ReloadConfig --> UpdateLog{日志级别<br/>是否变更?}
    UpdateLog -->|是| SetLogLevel[设置新的日志级别]
    UpdateLog -->|否| NotifyUpdate
    SetLogLevel --> NotifyUpdate[设置配置更新标志]
    
    NotifyUpdate --> SMCheck{服务管理器<br/>检查更新标志}
    SMCheck -->|有更新| SendSignal[向所有子进程发送<br/>CONFIG_UPDATE信号]
    SMCheck -->|无更新| Sleep
    
    %% 子进程处理配置更新
    SendSignal --> ChildReceive[子进程接收信号]
    ChildReceive --> ChildReload[子进程重新加载配置]
    ChildReload --> MPReload[通知消息处理器<br/>重新加载规则]
    MPReload --> ClearRules[清空现有规则]
    ClearRules --> LoadNewRules[加载新的<br/>direct_kafka_rule规则]
    LoadNewRules --> RulesActive[新规则立即生效]
    RulesActive --> Sleep

    %% 正常消息处理流程
    MainLoop --> MsgProcess[处理客户端消息]
    MsgProcess --> UseRules[使用最新的规则<br/>进行消息路由]
    UseRules --> WriteKafka[写入对应的Kafka主题]
    WriteKafka --> MainLoop

    %% 样式
    classDef startStyle fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef processStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef decisionStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef updateStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef endStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class Start,LoadConfig startStyle
    class StartMain,StartSM,StartTCP,StartChild,StartMP,StartKW,ReloadConfig,SetLogLevel,SendSignal,ChildReceive,ChildReload,MPReload processStyle
    class MainLoop,CheckConfig,UpdateLog,SMCheck decisionStyle
    class NotifyUpdate,ClearRules,LoadNewRules,RulesActive updateStyle
    class Sleep,MsgProcess,UseRules,WriteKafka endStyle