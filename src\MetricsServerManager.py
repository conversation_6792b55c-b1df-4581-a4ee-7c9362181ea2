#!/usr/bin/python
# -*- coding: utf-8 -*-
# 主进程服务管理器

import multiprocessing
import logging2
import socket
import time
import traceback
import threading
import sys
import select
from MetricsConnectionManager import MetricsConnectionManager

class ThreadSafeCounter:
    def __init__(self, initial_value=0):
        self.value = initial_value
        self._lock = threading.Lock()
        self.max_value = sys.maxsize

    def increment(self):
        with self._lock:
            if self.value >= self.max_value:
                self.value = 0
            else:
                self.value += 1
            return self.value

    def get_value(self):
        with self._lock:
            return self.value

class MetricsTcpService(threading.Thread):
    """TCP监听服务线程"""
    def __init__(self, isRun, serverCfg, counter, pidNums, parentPipeList):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_serverCfg = serverCfg
        self.m_counter = counter
        self.m_pidNums = pidNums
        self.m_parentPipeList = parentPipeList
        self.m_socket = None

    def bindSocket(self):
        self.m_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            # 绑定IP地址和端口号
            self.m_socket.bind((self.m_serverCfg['server_ip'], int(self.m_serverCfg['server_port'])))
            # 设置最大连接数为10
            self.m_socket.listen(10)
            logging2.info("TCP服务绑定成功 ip:port=(%s:%s)" % (self.m_serverCfg['server_ip'], self.m_serverCfg['server_port']))
            # 设置套接字为非阻塞模式
            self.m_socket.setblocking(0)
            return 0
        except socket.error as err:
            stack_trace = traceback.format_exc()
            logging2.error("TCP绑定失败: %s" % stack_trace)
            return -1
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error("TCP绑定异常: %s" % stack_trace)
            return -1

    def setRunState(self, isRun):
        self.m_isRun = isRun

    def run(self):
        try:
            if self.m_socket.fileno() == -1:
                raise ValueError("Socket file descriptor is invalid.")
            
            while self.m_isRun: 
                readable, _, _ = select.select([self.m_socket], [], [], 0.5)  # 0.5秒超时
                if readable:
                    client_socket, addr = self.m_socket.accept()               
                    logging2.warn("接受新连接 %s:%s" % (addr[0], addr[1]))
                    # 通过管道将客户端套接字传递给子进程
                    parentPipeIdx = int(self.m_counter.increment()) % self.m_pidNums
                    self.m_parentPipeList[parentPipeIdx].send((client_socket, addr))
                    logging2.debug("连接已发送到子进程 parentPipeIdx=%d" % parentPipeIdx)
            
            logging2.warn("TCP监听线程退出!")
        except socket.error as err:
            stack_trace = traceback.format_exc()
            logging2.error("TCP监听Socket错误: %s" % stack_trace)
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error("TCP监听异常: %s" % stack_trace)

class MetricsServerManager(threading.Thread):
    """主进程服务管理器"""
    def __init__(self, isRun, cfgObj, cfgFile):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_cfgObj = cfgObj
        self.m_cfgPidNums = int(self.m_cfgObj.get_single_value('common', 'workPidNums'))
        self.m_parentPipe = [None] * self.m_cfgPidNums
        self.m_childConn = [None] * self.m_cfgPidNums
        self.m_childPid = [None] * self.m_cfgPidNums
        self.m_connectionMgrObj = [None] * self.m_cfgPidNums
        self.m_counter = ThreadSafeCounter()
        self.m_listenThreads = []
        self.m_cfgFile = cfgFile
        self.restart_counts = [0] * self.m_cfgPidNums
        self.last_restart_time = [0] * self.m_cfgPidNums
        self.m_configUpdateFlag = False  # 配置更新标志

        # 创建子进程
        if self.main_process() < 0:
            self.exitDeal()
            logging2.error("创建子进程失败!")
            raise Exception("创建子进程失败!")
        
        # 监听端口
        if self.listenPort() < 0:
            self.exitDeal()
            logging2.error("端口监听失败!")
            raise Exception("端口监听失败!")
        
    def setRunState(self, isRun):
        self.m_isRun = isRun
        self.exitDeal()
    
    def getRunState(self):
        return self.m_isRun
    
    def notifyConfigUpdate(self):
        """通知配置更新"""
        self.m_configUpdateFlag = True
        logging2.info("配置更新通知已设置")

    def main_process(self):
        """创建子进程"""
        try:
            # 创建管道
            for i in range(0, self.m_cfgPidNums):
                # 创建父进程和子进程之间的管道
                self.m_parentPipe[i], self.m_childConn[i] = multiprocessing.Pipe()
                # 创建子进程
                self.m_connectionMgrObj[i] = MetricsConnectionManager(self.m_cfgFile)
                self.m_childPid[i] = multiprocessing.Process(
                    target=self.m_connectionMgrObj[i].init_child_process, 
                    args=(self.m_childConn[i],))
                self.m_childPid[i].start()
                self.last_restart_time[i] = time.time()

                logging2.warn("子进程创建成功 pid[%s]" % self.m_childPid[i].pid)
            return 0    
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error("创建子进程异常: %s" % stack_trace)
            return -1

    def listenPort(self):
        """创建端口监听线程"""
        try:
            serverList = self.m_cfgObj.get_group_value('server')
            serverNums = len(serverList)
            logging2.debug("服务器配置列表: %s" % serverList)
            
            for i in range(0, serverNums):
                # 创建监听线程，每个端口一个线程
                tcpServiceObj = MetricsTcpService(
                    self.m_isRun, serverList[i], self.m_counter, 
                    self.m_cfgPidNums, self.m_parentPipe)
                if tcpServiceObj.bindSocket() < 0:
                    logging2.error("创建socket失败!")
                    return -1
                tcpServiceObj.start()
                self.m_listenThreads.append(tcpServiceObj)
            return 0    
        except Exception as err:
            logging2.error("创建监听线程失败!")
            traceback.print_exc()
            logging2.error(str(err))
            return -1

    def exitDeal(self):
        """退出处理"""
        try:
            # 停止监听线程
            for i in range(0, len(self.m_listenThreads)):
                self.m_listenThreads[i].setRunState(False)
                self.m_listenThreads[i].join()

            logging2.warn("监听线程已退出!")

            # 停止子进程
            for i in range(0, self.m_cfgPidNums):
                self.m_parentPipe[i].send((False, False))
                self.m_parentPipe[i].close()
                self.m_childConn[i].close()
                self.m_childPid[i].join()
            return 0
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error("退出处理异常: %s" % stack_trace)
            return -1

    def run(self):
        """监控子进程状态"""
        while True:
            if not self.m_isRun:
                logging2.warn("主服务管理器线程退出!")
                break
            try:
                # 检查配置更新标志
                if self.m_configUpdateFlag:
                    self.m_configUpdateFlag = False
                    logging2.info("检测到配置更新，通知子进程重新加载规则")
                    # 通过管道发送配置更新信号给所有子进程
                    for i in range(0, self.m_cfgPidNums):
                        try:
                            # 发送配置更新信号 ("CONFIG_UPDATE", None)
                            self.m_parentPipe[i].send(("CONFIG_UPDATE", None))
                            logging2.debug("已向子进程 %d 发送配置更新信号" % i)
                        except Exception as e:
                            logging2.error("发送配置更新信号到子进程 %d 失败: %s" % (i, str(e)))
                
                # 检查子进程状态，如果异常退出则重启
                for i in range(0, self.m_cfgPidNums):
                    if not self.m_childPid[i].is_alive():
                        current_time = time.time()
                        if current_time - self.last_restart_time[i] < 60:
                            self.restart_counts[i] += 1
                        else:
                            self.restart_counts[i] = 1
                        self.last_restart_time[i] = current_time
                        
                        if self.restart_counts[i] > 3:
                            logging2.error("进程 %d 在1分钟内重启超过3次，退出主进程。" % i)
                            sys.exit(-1)  # 直接退出主进程  
                        
                        # 重启子进程
                        self.m_connectionMgrObj[i] = MetricsConnectionManager(self.m_cfgFile)
                        self.m_childPid[i] = multiprocessing.Process(
                            target=self.m_connectionMgrObj[i].init_child_process, 
                            args=(self.m_childConn[i],))
                        self.m_childPid[i].start()
                        logging2.warn("子进程 %d 重启成功，新pid: %s" % (i, self.m_childPid[i].pid))
                        
                time.sleep(5)
            except Exception as err:
                stack_trace = traceback.format_exc()
                logging2.error("监控子进程异常: %s" % stack_trace)
                self.exitDeal()
                time.sleep(10)