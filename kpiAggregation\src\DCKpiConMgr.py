#!/usr/bin/python
# -*- coding: utf-8 -*-
#子进程接收连接，并进行管理

import asyncio
import threading
import logging2
import time
import traceback
import json
from DCConfig import DCConfig
import signal
import queue
from DCKpiMesParse import DCKpiMesParse
from DCKpiMesTrans import DCKpiMesTrans
from DCKpiMesOut import DCKpiMesOut
from DCKpiDbLoad import DCKpiDbLoad
from DCKpiStatistics import DCKpiStatistics

class DCKpiConMgr():
    def __init__(self, cfgFile):
        self.m_isRun = True
        self.m_cfgFile = cfgFile
        self.m_configObj = None
        self.m_taskQueue = None
        self.m_outQueue = None
        self.m_mesParseList = []
        self.m_mesTransList = []
        self.m_mesOutList = []
        self.m_dbLoad = None
        self.m_statistics = None
        self.m_moduleName = "RECV"
        #接收到指定的连接后接收消息，并做消息是否为json格式的校验，写队列
        pass
    
    def handle_sigterm(self, signum, frame):
        logging2.warn("child Received SIGTERM, shutting down gracefully.")
        self.setRunState(False)

    def setRunState(self, isRun):
        self.m_isRun = isRun

    def init_child_process(self, conn):
        try:
            config_node = ['log', 'common', 'kafka', 'server', 'mes_rule']
            self.m_configObj = DCConfig(self.m_cfgFile, config_node)  # 配置文件名，可以根据需要修改 

            #启动日志
            logging2.start(self.m_configObj.get_single_value('log', 'logpath'), 'kpiAggC', self.m_configObj.get_single_value('log', 'loglevel'))
            logging2.debug("start child_process!")

            signal.signal(signal.SIGINT, self.handle_sigterm)  # 由Interrupt Key产生，通常是CTRL+C或者DELETE产生的中断
            signal.signal(signal.SIGQUIT, self.handle_sigterm)
            signal.signal(signal.SIGTERM, self.handle_sigterm)

            asyncio.run(self.child_process(conn))
        except Exception as err:
            traceback.print_exc()
            logging2.error(str(err))                
        finally:
            return
    #子进程用于初始化消息接收模块以及消息的处理
    async def child_process(self, conn):   
        # loop = asyncio.new_event_loop()
        # asyncio.set_event_loop(loop)
        loop = asyncio.get_running_loop()
        tasks = []
        try:
            if not self.startParse():
                logging2.error("startParse failed..")
                self.m_isRun = False

            while self.m_isRun:
                # 从管道接收客户端套接字
                client_socket, addr = await loop.run_in_executor(None, conn.recv) #conn.recv()
                if not client_socket:
                    logging2.warn("child_process stop..")
                    break
                logging2.warn("Received connection from parent process[%s:%s]"%(addr[0], addr[1]))
                # 使用异步事件循环处理客户端连接
                # asyncio.run(self.handle_client(client_socket))
                reader, writer = await asyncio.open_connection(sock=client_socket)
                task = asyncio.create_task(self.handle_client(reader, writer, addr))
                tasks.append(task)
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))                
        finally:
            for task in tasks:
                task.cancel()
            await asyncio.gather(*tasks, return_exceptions=True)
            self.setRunState(False)
            # loop.close()
            self.exitDeal()
            logging2.stop() 
            logging2.warn("exit child_process")


    #用协程方式处理每个客户端连接
    async def handle_client(self, reader, writer, addr):
        logging2.warn("handle_client start process[%s:%s]"%(addr[0], addr[1]))
        try:
            # reader, writer = await asyncio.open_connection(sock=client_socket)
            buffer = b''
            while self.m_isRun:
                data = await reader.read(1024)
                if not data:
                    logging2.warn("Connection with [%s:%s] closed by the client"%(addr[0], addr[1]))
                    break
                
                logging2.debug("Received data:%s"%(data))
                buffer += data
                messages = buffer.split(b'\n')
                buffer = messages.pop()  # 最后一个可能是不完整的消息

                for message in messages:
                    if message:
                        try:
                            # 解析JSON字符串
                            json_obj = json.loads(message)
                            logging2.debug("Received JSON:%s"%(message))
                            self.m_statistics.kpi_statistics(self.m_moduleName)
                            self.m_taskQueue.put(message, block=False)
                        except queue.Full:
                            logging2.warn("queue full, discard the message:%s"%(message))
                        except json.JSONDecodeError as e:
                            logging2.error("Failed to decode errorinfo:%s, JSON:%s"%(e, message))
                        except Exception as err:
                            logging2.error("Failed to decode errorinfo:%s, JSON:%s"%(str(err), message))
        except asyncio.CancelledError:
            logging2.info("task was cancelled")
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
        finally:
            writer.close()  # 直接关闭原始的 client_socket

    
    def startParse(self):
        logging2.debug("startParse start..")
        try:
            self.m_taskQueue = queue.Queue(maxsize=int(self.m_configObj.get_single_value('common', 'queue_size')))
            self.m_outQueue = queue.Queue(maxsize=int(self.m_configObj.get_single_value('common', 'queue_size')))
            threadNum = int(self.m_configObj.get_single_value('common', 'deal_thread_num'))
            outThreadNums = int(self.m_configObj.get_single_value('common', 'out_thread_num'))
            self.m_mesParseList = [None] * threadNum
            self.m_mesTransList = [None] * threadNum
            self.m_mesOutList   = [None] * outThreadNums
            self.m_dbLoad = DCKpiDbLoad(self.m_isRun, self.m_configObj)
            self.m_dbLoad.start()

            self.m_statistics = DCKpiStatistics(self.m_isRun)
            self.m_statistics.start()

            for i in range( 0, outThreadNums ):
                self.m_mesOutList[i] = DCKpiMesOut(self.m_isRun, self.m_configObj, self.m_outQueue, self.m_dbLoad, self.m_statistics)
                self.m_mesOutList[i].start()

            for i in range( 0, threadNum ):
                self.m_mesTransList[i] = DCKpiMesTrans(self.m_isRun, self.m_configObj, self.m_outQueue)
                self.m_mesTransList[i].start()

            for i in range( 0, threadNum ):
                self.m_mesParseList[i] = DCKpiMesParse(self.m_isRun, self.m_configObj, self.m_taskQueue, self.m_mesTransList, self.m_dbLoad)
                self.m_mesParseList[i].start()
            return True
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return False
        finally:
            pass

    def exitDeal(self):
        try:
            if self.m_dbLoad:
                self.m_dbLoad.setRunState(False)
            
            for mesParseObj in self.m_mesParseList:
                if mesParseObj is not None:
                    mesParseObj.setRunState(False)

            for mesParseObj in self.m_mesParseList:
                if mesParseObj is not None:
                    mesParseObj.join()
            
            for mesTransObj in self.m_mesTransList:
                if mesTransObj is not None:
                    mesTransObj.setRunState(False)

            for mesTransObj in self.m_mesTransList:
                if mesTransObj is not None:
                    mesTransObj.join()

            for mesOutObj in self.m_mesOutList:
                if mesOutObj is not None:
                    mesOutObj.setRunState(False)

            for mesOutObj in self.m_mesOutList:
                if mesOutObj is not None:
                    mesOutObj.join()

            if self.m_statistics:
                self.m_statistics.setRunState(False)
                self.m_statistics.join()        

            if self.m_dbLoad:
                self.m_dbLoad.join()
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
        finally:
            pass