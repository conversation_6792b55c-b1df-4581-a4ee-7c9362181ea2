#!/usr/bin/python
# -*- coding: utf-8 -*-

import sys
import time
import signal
import os
import logging2
from DCConfig import DCConfig
from MetricsServerManager import MetricsServerManager

global g_isRun
g_isRun = True

def handle_sigterm(signum, frame):
    logging2.warn("Received SIGTERM, shutting down gracefully.")
    global g_isRun
    g_isRun = False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(" e.g.: %s xmlfile" % sys.argv[0])
        sys.exit()
    
    #1.加载配置文件
    config_node = ['log', 'common', 'server', 'kafka', 'direct_kafka_rule']
    configObj = DCConfig(sys.argv[1], config_node)  # 配置文件名

    logging2.start(configObj.get_single_value('log', 'logpath'), 'metricsToKafka', configObj.get_single_value('log', 'loglevel'))

    signal.signal(signal.SIGINT, handle_sigterm)  # 由Interrupt Key产生，通常是CTRL+C或者DELETE产生的中断
    signal.signal(signal.SIGQUIT, handle_sigterm)
    signal.signal(signal.SIGTERM, handle_sigterm)

    metricsServManObj = None
    #2.初始化主进程
    try:
        metricsServManObj = MetricsServerManager(g_isRun, configObj, sys.argv[1])    
        metricsServManObj.start()
    except Exception as err:
        logging2.error("MetricsServerManager start failed!")
        logging2.stop() 
        exit(0)

    logging2.warn("Metrics To Kafka Service start success!")

    while g_isRun:
        if configObj.update_config():
            cfg_value = configObj.get_single_value('log', 'loglevel')
            if cfg_value != None:
                logging2.reSetLogLevel(cfg_value)
                logging2.warn("reSetLogLevel[%s]" % cfg_value)
            # 通知服务管理器配置已更新
            if metricsServManObj:
                metricsServManObj.notifyConfigUpdate()
        if metricsServManObj.getRunState() == False:
            g_isRun = False
            break
        time.sleep(10)  

    logging2.warn("Metrics To Kafka Service is exiting!")
    
    try:
        #3.捕获退出的信号
        metricsServManObj.setRunState(g_isRun)
        metricsServManObj.join()
        logging2.stop()  
    except Exception as err:
        logging2.error("MetricsServerManager exit error!")
        logging2.stop() 
        exit(0)