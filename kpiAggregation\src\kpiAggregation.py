#!/usr/bin/python
# -*- coding: utf-8 -*-

import sys
import time
import _thread
import signal
import logging2
from DCConfig import DCConfig
from DCKpiAggServMan import DCKpiAggServMan

global g_isRun
g_isRun = True

def handle_sigterm(signum, frame):
    logging2.warn("Received SIGTERM, shutting down gracefully.")
    global g_isRun
    g_isRun = False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(" e.g.: %s xmlfile" % sys.argv[0])
        sys.exit()
    
    #1.加载配置文件
    config_node = ['log', 'common', 'server', 'mes_rule']
    configObj = DCConfig(sys.argv[1], config_node)  # 配置文件名，可以根据需要修改

    logging2.start(configObj.get_single_value('log', 'logpath'), 'kpiAgg', configObj.get_single_value('log', 'loglevel'))
    # _thread.start_new_thread(loadCfgLog, (configObj,))

    signal.signal(signal.SIGINT, handle_sigterm)  # 由Interrupt Key产生，通常是CTRL+C或者DELETE产生的中断
    signal.signal(signal.SIGQUIT, handle_sigterm)
    signal.signal(signal.SIGTERM, handle_sigterm)

    kpiServManObj = None
    #2.初始化主进程
    try:
        kpiServManObj = DCKpiAggServMan(g_isRun, configObj, sys.argv[1])    
        kpiServManObj.start()
    except Exception as err:
        logging2.error("DCKpiAggServMan start failed!")
        logging2.stop() 
        exit(0)

    logging2.warn("KPI Aggregation Service start sucess!")

    while g_isRun:
        if configObj.update_config():
            cfg_value = configObj.get_single_value('log', 'loglevel')
            if cfg_value != None:
                logging2.reSetLogLevel(cfg_value)
                logging2.warn("reSetLogLevel[%s]" % cfg_value)
        if kpiServManObj.getRunState() == False:
            g_isRun = False
            break
        time.sleep(10)  

    logging2.warn("KPI Aggregation Service is exiting!")
    
    try:
        #3.捕获退出的信号
        kpiServManObj.setRunState(g_isRun)
        kpiServManObj.join()
        logging2.stop()  
    except Exception as err:
        logging2.error("DCKpiAggServMan exit error!")
        logging2.stop() 
        exit(0)