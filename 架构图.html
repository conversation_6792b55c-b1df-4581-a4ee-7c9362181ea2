<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 1779.9468994140625 1626.5999755859375" style="max-width: 1779.9468994140625px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-1754291433215-aruabu25b"><style>#mermaid-svg-1754291433215-aruabu25b{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#616161;}#mermaid-svg-1754291433215-aruabu25b .error-icon{fill:#f2dede;}#mermaid-svg-1754291433215-aruabu25b .error-text{fill:#a1260d;stroke:#a1260d;}#mermaid-svg-1754291433215-aruabu25b .edge-thickness-normal{stroke-width:2px;}#mermaid-svg-1754291433215-aruabu25b .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-1754291433215-aruabu25b .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-1754291433215-aruabu25b .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-1754291433215-aruabu25b .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-1754291433215-aruabu25b .marker{fill:#616161;stroke:#616161;}#mermaid-svg-1754291433215-aruabu25b .marker.cross{stroke:#616161;}#mermaid-svg-1754291433215-aruabu25b svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-1754291433215-aruabu25b .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#616161;}#mermaid-svg-1754291433215-aruabu25b .cluster-label text{fill:#424242;}#mermaid-svg-1754291433215-aruabu25b .cluster-label span,#mermaid-svg-1754291433215-aruabu25b p{color:#424242;}#mermaid-svg-1754291433215-aruabu25b .label text,#mermaid-svg-1754291433215-aruabu25b span,#mermaid-svg-1754291433215-aruabu25b p{fill:#616161;color:#616161;}#mermaid-svg-1754291433215-aruabu25b .node rect,#mermaid-svg-1754291433215-aruabu25b .node circle,#mermaid-svg-1754291433215-aruabu25b .node ellipse,#mermaid-svg-1754291433215-aruabu25b .node polygon,#mermaid-svg-1754291433215-aruabu25b .node path{fill:#fdf6e3;stroke:#d3af86;stroke-width:1px;}#mermaid-svg-1754291433215-aruabu25b .flowchart-label text{text-anchor:middle;}#mermaid-svg-1754291433215-aruabu25b .node .label{text-align:center;}#mermaid-svg-1754291433215-aruabu25b .node.clickable{cursor:pointer;}#mermaid-svg-1754291433215-aruabu25b .arrowheadPath{fill:#02091c;}#mermaid-svg-1754291433215-aruabu25b .edgePath .path{stroke:#616161;stroke-width:2.0px;}#mermaid-svg-1754291433215-aruabu25b .flowchart-link{stroke:#616161;fill:none;}#mermaid-svg-1754291433215-aruabu25b .edgeLabel{background-color:#fdf6e399;text-align:center;}#mermaid-svg-1754291433215-aruabu25b .edgeLabel rect{opacity:0.5;background-color:#fdf6e399;fill:#fdf6e399;}#mermaid-svg-1754291433215-aruabu25b .labelBkg{background-color:rgba(253, 246, 227, 0.5);}#mermaid-svg-1754291433215-aruabu25b .cluster rect{fill:rgba(238, 232, 213, 0.5);stroke:#b49471;stroke-width:1px;}#mermaid-svg-1754291433215-aruabu25b .cluster text{fill:#424242;}#mermaid-svg-1754291433215-aruabu25b .cluster span,#mermaid-svg-1754291433215-aruabu25b p{color:#424242;}#mermaid-svg-1754291433215-aruabu25b div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:rgba(181, 137, 0, 0.67);border:1px solid #b49471;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-1754291433215-aruabu25b .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#616161;}#mermaid-svg-1754291433215-aruabu25b :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-svg-1754291433215-aruabu25b .processStyle&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .processStyle span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .threadStyle&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .threadStyle span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .queueStyle&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .queueStyle span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .kafkaStyle&gt;*{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .kafkaStyle span{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .configStyle&gt;*{fill:#fff8e1!important;stroke:#f57c00!important;stroke-width:2px!important;}#mermaid-svg-1754291433215-aruabu25b .configStyle span{fill:#fff8e1!important;stroke:#f57c00!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1754291433215-aruabu25b_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1754291433215-aruabu25b_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1754291433215-aruabu25b_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1754291433215-aruabu25b_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1754291433215-aruabu25b_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g id="配置文件" class="cluster default flowchart-label"><rect height="143.4000015258789" width="254.0625" y="0" x="139.63749504089355" ry="0" rx="0" style=""/><g transform="translate(234.66874504089355, 0)" class="cluster-label"><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">配置文件</span></div></foreignObject></g></g><g id="Kafka集群" class="cluster default flowchart-label"><rect height="83.4000015258789" width="1269.2093906402588" y="1527.2000045776367" x="415.4343681335449" ry="0" rx="0" style=""/><g transform="translate(1014.032811164856, 1527.2000045776367)" class="cluster-label"><foreignObject height="20.80000114440918" width="72.01250457763672"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka集群</span></div></foreignObject></g></g><g id="subGraph12" class="cluster default flowchart-label"><rect height="578.9999961853027" width="557.5000038146973" y="877.4000072479248" x="307.68124198913574" ry="0" rx="0" style=""/><g transform="translate(453.94374084472656, 877.4000072479248)" class="cluster-label"><foreignObject height="20.80000114440918" width="264.9750061035156"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">子进程2 (MetricsConnectionManager)</span></div></foreignObject></g></g><g id="subGraph8" class="cluster default flowchart-label"><rect height="578.9999961853027" width="878.7656364440918" y="877.4000072479248" x="885.181245803833" ry="0" rx="0" style=""/><g transform="translate(1192.076560974121, 877.4000072479248)" class="cluster-label"><foreignObject height="20.80000114440918" width="264.9750061035156"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">子进程1 (MetricsConnectionManager)</span></div></foreignObject></g></g><g id="subGraph4" class="cluster default flowchart-label"><rect height="442.4000015258789" width="1276.428134918213" y="364.2000045776367" x="404.4499969482422" ry="0" rx="0" style=""/><g transform="translate(915.9203128814697, 364.2000045776367)" class="cluster-label"><foreignObject height="20.80000114440918" width="253.4875030517578"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">服务管理器 (MetricsServerManager)</span></div></foreignObject></g></g><g id="subGraph1" class="cluster default flowchart-label"><rect height="293.40000343322754" width="487.16250228881836" y="0" x="786.5374975204468" ry="0" rx="0" style=""/><g transform="translate(931.781247138977, 0)" class="cluster-label"><foreignObject height="20.80000114440918" width="196.6750030517578"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">主进程 (metricsToKafka.py)</span></div></foreignObject></g></g><g id="客户端" class="cluster default flowchart-label"><rect height="85.79999923706055" width="384.4499969482422" y="364.2000045776367" x="0" ry="0" rx="0" style=""/><g transform="translate(168.2249984741211, 364.2000045776367)" class="cluster-label"><foreignObject height="20.80000114440918" width="48"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">客户端</span></div></foreignObject></g></g><g id="消息处理线程池2" class="cluster default flowchart-label"><rect height="85.79999923706055" width="358.8000030517578" y="988.2000064849854" x="407.03124237060547" ry="0" rx="0" style=""/><g transform="translate(526.2312431335449, 988.2000064849854)" class="cluster-label"><foreignObject height="20.80000114440918" width="120.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息处理线程池2</span></div></foreignObject></g></g><g id="Kafka写入线程池2" class="cluster default flowchart-label"><rect height="85.79999923706055" width="456.15000343322754" y="1345.600004196167" x="389.03124237060547" ry="0" rx="0" style=""/><g transform="translate(552.9062395095825, 1345.600004196167)" class="cluster-label"><foreignObject height="20.80000114440918" width="128.40000915527344"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka写入线程池2</span></div></foreignObject></g></g><g id="队列2" class="cluster default flowchart-label"><rect height="171.5999984741211" width="277.90625381469727" y="1124.000005722046" x="442.47811698913574" ry="0" rx="0" style=""/><g transform="translate(561.2312431335449, 1124.000005722046)" class="cluster-label"><foreignObject height="20.80000114440918" width="40.400001525878906"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">队列2</span></div></foreignObject></g></g><g id="消息处理线程池1" class="cluster default flowchart-label"><rect height="85.79999923706055" width="530.0125045776367" y="988.2000064849854" x="1005.4281215667725" ry="0" rx="0" style=""/><g transform="translate(1210.2343730926514, 988.2000064849854)" class="cluster-label"><foreignObject height="20.80000114440918" width="120.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息处理线程池1</span></div></foreignObject></g></g><g id="Kafka写入线程池1" class="cluster default flowchart-label"><rect height="85.79999923706055" width="709.7562618255615" y="1345.600004196167" x="1034.1906204223633" ry="0" rx="0" style=""/><g transform="translate(1324.8687467575073, 1345.600004196167)" class="cluster-label"><foreignObject height="20.80000114440918" width="128.40000915527344"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka写入线程池1</span></div></foreignObject></g></g><g id="队列1" class="cluster default flowchart-label"><rect height="171.5999984741211" width="448.2125053405762" y="1124.000005722046" x="1041.7812461853027" ry="0" rx="0" style=""/><g transform="translate(1245.6874980926514, 1124.000005722046)" class="cluster-label"><foreignObject height="20.80000114440918" width="40.400001525878906"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">队列1</span></div></foreignObject></g></g><g id="TCP监听线程池" class="cluster default flowchart-label"><rect height="106.5999984741211" width="559.7718753814697" y="520.8000049591064" x="725.0437488555908" ry="0" rx="0" style=""/><g transform="translate(951.0359373092651, 520.8000049591064)" class="cluster-label"><foreignObject height="20.80000114440918" width="107.7874984741211"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">TCP监听线程池</span></div></foreignObject></g></g><g id="管道通信" class="cluster default flowchart-label"><rect height="83.4000015258789" width="1204.7750091552734" y="698.2000045776367" x="448.24999809265137" ry="0" rx="0" style=""/><g transform="translate(1018.6375026702881, 698.2000045776367)" class="cluster-label"><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">管道通信</span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP LE-CFG" id="L-MP-CFG-0" d="M1049.053,89.6L1036.01,98.567C1022.966,107.533,996.88,125.467,983.837,140.333C970.794,155.2,970.794,167,961.064,178.815C951.335,190.63,931.875,202.46,922.146,208.375L912.416,214.29"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP LE-LOG" id="L-MP-LOG-0" d="M1055.692,89.6L1045.975,98.567C1036.257,107.533,1016.822,125.467,1007.105,140.333C997.387,155.2,997.387,167,997.387,177.917C997.387,188.833,997.387,198.867,997.387,203.883L997.387,208.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP LE-SIG" id="L-MP-SIG-0" d="M1094.489,89.6L1104.207,98.567C1113.924,107.533,1133.359,125.467,1143.076,140.333C1152.794,155.2,1152.794,167,1152.794,177.917C1152.794,188.833,1152.794,198.867,1152.794,203.883L1152.794,208.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SM LE-T1" id="L-SM-T1-0" d="M1073.485,425L1076.428,429.167C1079.37,433.333,1085.255,441.667,1088.198,451.733C1091.141,461.8,1091.141,473.6,1091.141,485.4C1091.141,497.2,1091.141,509,1084.474,518.635C1077.807,528.27,1064.474,535.74,1057.807,539.475L1051.141,543.21"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SM LE-T2" id="L-SM-T2-0" d="M997.344,424.06L981.156,428.383C964.969,432.706,932.594,441.353,916.406,451.577C900.219,461.8,900.219,473.6,900.219,485.4C900.219,497.2,900.219,509,894.505,518.588C888.791,528.175,877.363,535.551,871.649,539.238L865.935,542.926"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SM LE-P1" id="L-SM-P1-0" d="M1124.344,412.448L1198.658,418.707C1272.973,424.965,1421.602,437.483,1495.917,449.641C1570.231,461.8,1570.231,473.6,1570.231,485.4C1570.231,497.2,1570.231,509,1570.231,523.783C1570.231,538.567,1570.231,556.333,1570.231,574.1C1570.231,591.867,1570.231,609.633,1570.231,624.417C1570.231,639.2,1570.231,651,1570.231,662.8C1570.231,674.6,1570.231,686.4,1539.881,698.209C1509.531,710.018,1448.831,721.835,1418.481,727.744L1388.13,733.653"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SM LE-P2" id="L-SM-P2-0" d="M997.344,413.611L938.194,419.676C879.044,425.741,760.744,437.87,701.594,449.835C642.444,461.8,642.444,473.6,642.444,485.4C642.444,497.2,642.444,509,642.444,523.783C642.444,538.567,642.444,556.333,642.444,574.1C642.444,591.867,642.444,609.633,649.902,624.417C657.36,639.2,672.277,651,679.735,662.8C687.194,674.6,687.194,686.4,676.591,696.937C665.987,707.473,644.781,716.746,634.178,721.383L623.575,726.019"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SM LE-PN" id="L-SM-PN-0" d="M1124.344,412.246L1201.992,418.538C1279.64,424.831,1434.935,437.415,1512.583,449.608C1590.231,461.8,1590.231,473.6,1590.231,485.4C1590.231,497.2,1590.231,509,1590.231,523.783C1590.231,538.567,1590.231,556.333,1590.231,574.1C1590.231,591.867,1590.231,609.633,1590.231,624.417C1590.231,639.2,1590.231,651,1590.231,662.8C1590.231,674.6,1590.231,686.4,1590.231,695.583C1590.231,704.767,1590.231,711.333,1590.231,714.617L1590.231,717.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP1 LE-MP1" id="L-CP1-MP1-0" d="M1391.382,938.2L1399.608,942.367C1407.835,946.533,1424.288,954.867,1432.514,963.2C1440.741,971.533,1440.741,979.867,1440.741,987.317C1440.741,994.767,1440.741,1001.333,1440.741,1004.617L1440.741,1007.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP1 LE-MP2" id="L-CP1-MP2-0" d="M1320.7,938.2L1312.473,942.367C1304.247,946.533,1287.794,954.867,1279.567,963.2C1271.341,971.533,1271.341,979.867,1271.341,987.317C1271.341,994.767,1271.341,1001.333,1271.341,1004.617L1271.341,1007.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP1 LE-MP3" id="L-CP1-MP3-0" d="M1320.341,926.306L1283.79,932.455C1247.239,938.604,1174.136,950.902,1137.585,961.218C1101.034,971.533,1101.034,979.867,1101.034,987.317C1101.034,994.767,1101.034,1001.333,1101.034,1004.617L1101.034,1007.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP1 LE-KW1" id="L-CP1-KW1-0" d="M1320.341,924.432L1264.522,930.894C1208.703,937.355,1097.066,950.277,1041.247,960.905C985.428,971.533,985.428,979.867,985.428,991.183C985.428,1002.5,985.428,1016.8,985.428,1031.1C985.428,1045.4,985.428,1059.7,985.428,1071.017C985.428,1082.333,985.428,1090.667,985.428,1099C985.428,1107.333,985.428,1115.667,985.428,1126.983C985.428,1138.3,985.428,1152.6,985.428,1166.9C985.428,1181.2,985.428,1195.5,985.428,1209.8C985.428,1224.1,985.428,1238.4,985.428,1252.7C985.428,1267,985.428,1281.3,1015.155,1292.617C1044.881,1303.933,1104.334,1312.267,1134.061,1320.6C1163.787,1328.933,1163.787,1337.267,1161.303,1344.883C1158.818,1352.5,1153.849,1359.4,1151.364,1362.849L1148.88,1366.299"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP1 LE-KW2" id="L-CP1-KW2-0" d="M1391.741,927.981L1419.024,933.851C1446.307,939.72,1500.874,951.46,1528.157,961.497C1555.441,971.533,1555.441,979.867,1555.441,991.183C1555.441,1002.5,1555.441,1016.8,1555.441,1031.1C1555.441,1045.4,1555.441,1059.7,1555.441,1071.017C1555.441,1082.333,1555.441,1090.667,1555.441,1099C1555.441,1107.333,1555.441,1115.667,1555.441,1126.983C1555.441,1138.3,1555.441,1152.6,1555.441,1166.9C1555.441,1181.2,1555.441,1195.5,1555.441,1209.8C1555.441,1224.1,1555.441,1238.4,1555.441,1252.7C1555.441,1267,1555.441,1281.3,1530.766,1292.617C1506.092,1303.933,1456.743,1312.267,1432.068,1320.6C1407.394,1328.933,1407.394,1337.267,1400.482,1345.179C1393.571,1353.092,1379.748,1360.583,1372.837,1364.329L1365.925,1368.075"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP1 LE-KW3" id="L-CP1-KW3-0" d="M1391.741,924.968L1440.474,931.34C1489.207,937.712,1586.674,950.456,1635.407,960.995C1684.141,971.533,1684.141,979.867,1684.141,991.183C1684.141,1002.5,1684.141,1016.8,1684.141,1031.1C1684.141,1045.4,1684.141,1059.7,1684.141,1071.017C1684.141,1082.333,1684.141,1090.667,1684.141,1099C1684.141,1107.333,1684.141,1115.667,1684.141,1126.983C1684.141,1138.3,1684.141,1152.6,1684.141,1166.9C1684.141,1181.2,1684.141,1195.5,1684.141,1209.8C1684.141,1224.1,1684.141,1238.4,1684.141,1252.7C1684.141,1267,1684.141,1281.3,1684.141,1292.617C1684.141,1303.933,1684.141,1312.267,1684.141,1320.6C1684.141,1328.933,1684.141,1337.267,1672.527,1345.32C1660.914,1353.373,1637.687,1361.145,1626.073,1365.032L1614.46,1368.918"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP1 LE-MQ1" id="L-MP1-MQ1-0" d="M1440.741,1049L1440.741,1053.167C1440.741,1057.333,1440.741,1065.667,1440.741,1074C1440.741,1082.333,1440.741,1090.667,1440.741,1099C1440.741,1107.333,1440.741,1115.667,1419.872,1125.104C1399.002,1134.542,1357.264,1145.084,1336.395,1150.355L1315.526,1155.626"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP2 LE-MQ1" id="L-MP2-MQ1-0" d="M1271.341,1049L1271.341,1053.167C1271.341,1057.333,1271.341,1065.667,1271.341,1074C1271.341,1082.333,1271.341,1090.667,1271.341,1099C1271.341,1107.333,1271.341,1115.667,1271.306,1123.117C1271.271,1130.567,1271.202,1137.134,1271.167,1140.417L1271.133,1143.7"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP3 LE-MQ1" id="L-MP3-MQ1-0" d="M1101.034,1049L1101.034,1053.167C1101.034,1057.333,1101.034,1065.667,1101.034,1074C1101.034,1082.333,1101.034,1090.667,1101.034,1099C1101.034,1107.333,1101.034,1115.667,1121.903,1125.104C1142.773,1134.542,1184.511,1145.084,1205.38,1150.355L1226.249,1155.626"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MQ1 LE-KQ1" id="L-MQ1-KQ1-0" d="M1270.887,1184.8L1270.887,1188.967C1270.887,1193.133,1270.887,1201.467,1270.887,1208.917C1270.887,1216.367,1270.887,1222.933,1270.887,1226.217L1270.887,1229.5"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-KQ1 LE-KW1" id="L-KQ1-KW1-0" d="M1233.9,1270.6L1225.29,1274.767C1216.68,1278.933,1199.46,1287.267,1177.734,1295.6C1156.007,1303.933,1129.774,1312.267,1116.657,1320.6C1103.541,1328.933,1103.541,1337.267,1105.892,1344.871C1108.244,1352.475,1112.948,1359.351,1115.3,1362.788L1117.652,1366.226"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-KQ1 LE-KW2" id="L-KQ1-KW2-0" d="M1270.887,1270.6L1270.887,1274.767C1270.887,1278.933,1270.887,1287.267,1270.887,1295.6C1270.887,1303.933,1270.887,1312.267,1270.887,1320.6C1270.887,1328.933,1270.887,1337.267,1275.75,1345.071C1280.613,1352.875,1290.339,1360.15,1295.201,1363.788L1300.064,1367.425"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-KQ1 LE-KW3" id="L-KQ1-KW3-0" d="M1312.26,1270.6L1321.891,1274.767C1331.522,1278.933,1350.783,1287.267,1369.972,1295.6C1389.16,1303.933,1408.277,1312.267,1417.835,1320.6C1427.394,1328.933,1427.394,1337.267,1439.041,1345.32C1450.689,1353.374,1473.984,1361.148,1485.631,1365.035L1497.279,1368.922"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP2 LE-MP4" id="L-CP2-MP4-0" d="M624.919,938.2L632.621,942.367C640.323,946.533,655.727,954.867,663.429,963.2C671.131,971.533,671.131,979.867,671.131,987.317C671.131,994.767,671.131,1001.333,671.131,1004.617L671.131,1007.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP2 LE-MP5" id="L-CP2-MP5-0" d="M556.131,937.298L547.065,941.615C537.998,945.932,519.865,954.566,510.798,963.05C501.731,971.533,501.731,979.867,501.731,987.317C501.731,994.767,501.731,1001.333,501.731,1004.617L501.731,1007.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP2 LE-KW4" id="L-CP2-KW4-0" d="M556.131,927.778L527.948,933.682C499.765,939.585,443.398,951.393,415.215,961.463C387.031,971.533,387.031,979.867,387.031,991.183C387.031,1002.5,387.031,1016.8,387.031,1031.1C387.031,1045.4,387.031,1059.7,387.031,1071.017C387.031,1082.333,387.031,1090.667,387.031,1099C387.031,1107.333,387.031,1115.667,387.031,1126.983C387.031,1138.3,387.031,1152.6,387.031,1166.9C387.031,1181.2,387.031,1195.5,387.031,1209.8C387.031,1224.1,387.031,1238.4,387.031,1252.7C387.031,1267,387.031,1281.3,405.481,1292.617C423.931,1303.933,460.831,1312.267,479.281,1320.6C497.731,1328.933,497.731,1337.267,496.961,1344.74C496.19,1352.213,494.648,1358.826,493.878,1362.132L493.107,1365.438"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-CP2 LE-KW5" id="L-CP2-KW5-0" d="M627.531,928.194L653.915,934.029C680.298,939.863,733.065,951.532,759.448,961.532C785.831,971.533,785.831,979.867,785.831,991.183C785.831,1002.5,785.831,1016.8,785.831,1031.1C785.831,1045.4,785.831,1059.7,785.831,1071.017C785.831,1082.333,785.831,1090.667,785.831,1099C785.831,1107.333,785.831,1115.667,785.831,1126.983C785.831,1138.3,785.831,1152.6,785.831,1166.9C785.831,1181.2,785.831,1195.5,785.831,1209.8C785.831,1224.1,785.831,1238.4,785.831,1252.7C785.831,1267,785.831,1281.3,785.831,1292.617C785.831,1303.933,785.831,1312.267,785.831,1320.6C785.831,1328.933,785.831,1337.267,780.968,1345.071C776.106,1352.875,766.38,1360.15,761.517,1363.788L756.654,1367.425"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP4 LE-MQ2" id="L-MP4-MQ2-0" d="M671.131,1049L671.131,1053.167C671.131,1057.333,671.131,1065.667,671.131,1074C671.131,1082.333,671.131,1090.667,671.131,1099C671.131,1107.333,671.131,1115.667,663.693,1123.601C656.254,1131.535,641.377,1139.07,633.939,1142.838L626.5,1146.605"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP5 LE-MQ2" id="L-MP5-MQ2-0" d="M501.731,1049L501.731,1053.167C501.731,1057.333,501.731,1065.667,501.731,1074C501.731,1082.333,501.731,1090.667,501.731,1099C501.731,1107.333,501.731,1115.667,509.17,1123.601C516.608,1131.535,531.485,1139.07,538.924,1142.838L546.362,1146.605"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MQ2 LE-KQ2" id="L-MQ2-KQ2-0" d="M586.431,1184.8L586.431,1188.967C586.431,1193.133,586.431,1201.467,586.431,1208.917C586.431,1216.367,586.431,1222.933,586.431,1226.217L586.431,1229.5"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-KQ2 LE-KW4" id="L-KQ2-KW4-0" d="M561.104,1270.6L555.209,1274.767C549.313,1278.933,537.522,1287.267,520.402,1295.6C503.281,1303.933,480.831,1312.267,469.606,1320.6C458.381,1328.933,458.381,1337.267,460.733,1344.871C463.085,1352.475,467.789,1359.351,470.14,1362.788L472.492,1366.226"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-KQ2 LE-KW5" id="L-KQ2-KW5-0" d="M621.772,1270.6L629.999,1274.767C638.225,1278.933,654.678,1287.267,662.905,1295.6C671.131,1303.933,671.131,1312.267,671.131,1320.6C671.131,1328.933,671.131,1337.267,675.994,1345.071C680.857,1352.875,690.582,1360.15,695.445,1363.788L700.308,1367.425"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-C1 LE-T1" id="L-C1-T1-0" d="M314.012,423.8L314.012,428.167C314.012,432.533,314.012,441.267,433.327,451.533C552.642,461.8,791.271,473.6,910.585,485.4C1029.9,497.2,1029.9,509,1027.724,518.321C1025.548,527.643,1021.197,534.485,1019.021,537.906L1016.845,541.328"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-C2 LE-T1" id="L-C2-T1-0" d="M193.137,423.8L193.137,428.167C193.137,432.533,193.137,441.267,319.3,451.533C445.462,461.8,697.788,473.6,823.95,485.4C950.113,497.2,950.113,509,953.124,518.397C956.135,527.795,962.157,534.789,965.168,538.286L968.179,541.784"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-C3 LE-T2" id="L-C3-T2-0" d="M71.35,423.8L71.35,428.167C71.35,432.533,71.35,441.267,195.73,451.533C320.11,461.8,568.871,473.6,693.251,485.4C817.631,497.2,817.631,509,817.631,518.183C817.631,527.367,817.631,533.933,817.631,537.217L817.631,540.5"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-T1 LE-P1" id="L-T1-P1-0" d="M1053.591,587.65L1081.746,594.275C1109.902,600.9,1166.214,614.15,1224.481,626.675C1282.748,639.2,1342.971,651,1373.082,662.8C1403.194,674.6,1403.194,686.4,1399.144,695.881C1395.094,705.363,1386.994,712.526,1382.945,716.107L1378.895,719.689"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-T1 LE-P2" id="L-T1-P2-0" d="M992.819,602.4L992.35,606.567C991.881,610.733,990.944,619.067,931.34,629.133C871.735,639.2,753.465,651,694.329,662.8C635.194,674.6,635.194,686.4,631.498,695.854C627.802,705.309,620.409,712.418,616.713,715.972L613.017,719.526"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-T2 LE-P1" id="L-T2-P1-0" d="M875.219,583.928L917.672,591.174C960.125,598.419,1045.031,612.909,1112.263,626.055C1179.494,639.2,1229.05,651,1253.828,662.8C1278.606,674.6,1278.606,686.4,1286.253,696.418C1293.9,706.436,1309.193,714.672,1316.84,718.79L1324.487,722.908"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-T2 LE-P2" id="L-T2-P2-0" d="M817.631,602.4L817.631,606.567C817.631,610.733,817.631,619.067,773.225,629.133C728.819,639.2,640.006,651,595.6,662.8C551.194,674.6,551.194,686.4,554.638,695.834C558.082,705.268,564.97,712.336,568.414,715.87L571.858,719.404"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-P1 LE-CP1" id="L-P1-CP1-0" d="M1356.041,756.6L1356.041,760.767C1356.041,764.933,1356.041,773.267,1356.041,781.6C1356.041,789.933,1356.041,798.267,1356.041,808.333C1356.041,818.4,1356.041,830.2,1356.041,842C1356.041,853.8,1356.041,865.6,1356.041,874.783C1356.041,883.967,1356.041,890.533,1356.041,893.817L1356.041,897.1"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-P2 LE-CP2" id="L-P2-CP2-0" d="M591.831,756.6L591.831,760.767C591.831,764.933,591.831,773.267,591.831,781.6C591.831,789.933,591.831,798.267,591.831,808.333C591.831,818.4,591.831,830.2,591.831,842C591.831,853.8,591.831,865.6,591.831,874.783C591.831,883.967,591.831,890.533,591.831,893.817L591.831,897.1"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-KW1 LE-K1" id="L-KW1-K1-0" d="M1132.891,1406.4L1132.891,1410.567C1132.891,1414.733,1132.891,1423.067,1132.891,1431.4C1132.891,1439.733,1132.891,1448.067,1104.107,1458.133C1075.324,1468.2,1017.757,1480,988.974,1491.8C960.191,1503.6,960.191,1515.4,922.189,1526.532C884.188,1537.665,808.185,1548.13,770.183,1553.362L732.182,1558.595"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-KW2 LE-K2" id="L-KW2-K2-0" d="M1328.237,1406.4L1328.237,1410.567C1328.237,1414.733,1328.237,1423.067,1328.237,1431.4C1328.237,1439.733,1328.237,1448.067,1328.237,1458.133C1328.237,1468.2,1328.237,1480,1328.237,1491.8C1328.237,1503.6,1328.237,1515.4,1319.327,1525.119C1310.417,1534.837,1292.596,1542.475,1283.686,1546.294L1274.776,1550.112"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-KW3 LE-K3" id="L-KW3-K3-0" d="M1555.944,1406.4L1555.944,1410.567C1555.944,1414.733,1555.944,1423.067,1555.944,1431.4C1555.944,1439.733,1555.944,1448.067,1555.944,1458.133C1555.944,1468.2,1555.944,1480,1555.944,1491.8C1555.944,1503.6,1555.944,1515.4,1555.944,1524.583C1555.944,1533.767,1555.944,1540.333,1555.944,1543.617L1555.944,1546.9"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-KW4 LE-K1" id="L-KW4-K1-0" d="M487.731,1406.4L487.731,1410.567C487.731,1414.733,487.731,1423.067,487.731,1431.4C487.731,1439.733,487.731,1448.067,487.731,1458.133C487.731,1468.2,487.731,1480,487.731,1491.8C487.731,1503.6,487.731,1515.4,503.821,1525.256C519.91,1535.112,552.088,1543.023,568.178,1546.979L584.267,1550.935"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-KW5 LE-K2" id="L-KW5-K2-0" d="M728.481,1406.4L728.481,1410.567C728.481,1414.733,728.481,1423.067,728.481,1431.4C728.481,1439.733,728.481,1448.067,775.766,1458.133C823.051,1468.2,917.621,1480,964.906,1491.8C1012.191,1503.6,1012.191,1515.4,1033.18,1525.301C1054.17,1535.203,1096.149,1543.205,1117.138,1547.206L1138.128,1551.208"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-MP LE-SM" id="L-MP-SM-0" d="M1106.591,84.345L1131.109,94.188C1155.627,104.03,1204.664,123.715,1229.182,139.458C1253.7,155.2,1253.7,167,1253.7,183.317C1253.7,199.633,1253.7,220.467,1253.7,239.567C1253.7,258.667,1253.7,276.033,1253.7,290.617C1253.7,305.2,1253.7,317,1253.7,328.8C1253.7,340.6,1253.7,352.4,1233.003,362.904C1212.306,373.408,1170.912,382.616,1150.214,387.22L1129.517,391.824"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-XML LE-CFG" id="L-XML-CFG-0" d="M266.669,118.4L266.669,122.567C266.669,126.733,266.669,135.067,365.79,145.133C464.91,155.2,663.152,167,762.803,177.922C862.453,188.843,863.513,198.886,864.043,203.908L864.572,208.929"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-CFG LE-SM" id="L-CFG-SM-0" d="M867.987,268.4L867.987,272.567C867.987,276.733,867.987,285.067,867.987,295.133C867.987,305.2,867.987,317,867.987,328.8C867.987,340.6,867.987,352.4,888.685,362.904C909.382,373.408,950.776,382.616,971.473,387.22L992.17,391.824"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-SM LE-P1" id="L-SM-P1-1" d="M1124.344,414.502L1175.096,420.419C1225.849,426.335,1327.354,438.167,1378.107,449.984C1428.859,461.8,1428.859,473.6,1428.859,485.4C1428.859,497.2,1428.859,509,1428.859,523.783C1428.859,538.567,1428.859,556.333,1428.859,574.1C1428.859,591.867,1428.859,609.633,1390.896,624.417C1352.933,639.2,1277.007,651,1239.044,662.8C1201.081,674.6,1201.081,686.4,1221.574,697.815C1242.066,709.229,1283.051,720.258,1303.543,725.773L1324.035,731.287"/><path marker-end="url(#mermaid-svg-1754291433215-aruabu25b_flowchart-pointEnd)" style="fill:none;stroke-width:2px;stroke-dasharray:3;" class="edge-thickness-normal edge-pattern-dotted flowchart-link LS-SM LE-P2" id="L-SM-P2-1" d="M997.344,411.95L914.319,418.292C831.294,424.634,665.244,437.317,582.219,449.558C499.194,461.8,499.194,473.6,499.194,485.4C499.194,497.2,499.194,509,499.194,523.783C499.194,538.567,499.194,556.333,499.194,574.1C499.194,591.867,499.194,609.633,499.194,624.417C499.194,639.2,499.194,651,499.194,662.8C499.194,674.6,499.194,686.4,509.347,696.87C519.499,707.34,539.805,716.481,549.958,721.051L560.111,725.621"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1029.9000053405762, 485.40000438690186)" class="edgeLabel"><g transform="translate(-29.89375114440918, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="59.78750228881836"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">TCP连接</span></div></foreignObject></g></g><g transform="translate(950.1125030517578, 485.40000438690186)" class="edgeLabel"><g transform="translate(-29.89375114440918, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="59.78750228881836"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">TCP连接</span></div></foreignObject></g></g><g transform="translate(817.6312503814697, 485.40000438690186)" class="edgeLabel"><g transform="translate(-29.89375114440918, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="59.78750228881836"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">TCP连接</span></div></foreignObject></g></g><g transform="translate(1403.193754196167, 662.8000040054321)" class="edgeLabel"><g transform="translate(-32, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">负载均衡</span></div></foreignObject></g></g><g transform="translate(635.1937484741211, 662.8000040054321)" class="edgeLabel"><g transform="translate(-32, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">负载均衡</span></div></foreignObject></g></g><g transform="translate(1278.606252670288, 662.8000040054321)" class="edgeLabel"><g transform="translate(-32, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">负载均衡</span></div></foreignObject></g></g><g transform="translate(551.1937484741211, 662.8000040054321)" class="edgeLabel"><g transform="translate(-32, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">负载均衡</span></div></foreignObject></g></g><g transform="translate(1356.0406246185303, 842.0000066757202)" class="edgeLabel"><g transform="translate(-32, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">传递连接</span></div></foreignObject></g></g><g transform="translate(591.8312435150146, 842.0000066757202)" class="edgeLabel"><g transform="translate(-32, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">传递连接</span></div></foreignObject></g></g><g transform="translate(960.1906204223633, 1491.8000040054321)" class="edgeLabel"><g transform="translate(-16, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="32"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">写入</span></div></foreignObject></g></g><g transform="translate(1328.2374992370605, 1491.8000040054321)" class="edgeLabel"><g transform="translate(-16, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="32"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">写入</span></div></foreignObject></g></g><g transform="translate(1555.943754196167, 1491.8000040054321)" class="edgeLabel"><g transform="translate(-16, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="32"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">写入</span></div></foreignObject></g></g><g transform="translate(487.7312431335449, 1491.8000040054321)" class="edgeLabel"><g transform="translate(-16, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="32"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">写入</span></div></foreignObject></g></g><g transform="translate(1012.1906204223633, 1491.8000040054321)" class="edgeLabel"><g transform="translate(-16, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="32"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">写入</span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(861.3937520980835, 178.8000020980835)" class="edgeLabel"><g transform="translate(-24, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="48"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">热更新</span></div></foreignObject></g></g><g transform="translate(867.9874982833862, 328.80000400543213)" class="edgeLabel"><g transform="translate(-48, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="96"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">配置变更通知</span></div></foreignObject></g></g><g transform="translate(1428.859380722046, 574.100004196167)" class="edgeLabel"><g transform="translate(-74.7437515258789, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="149.4875030517578"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">CONFIG_UPDATE信号</span></div></foreignObject></g></g><g transform="translate(499.1937484741211, 574.100004196167)" class="edgeLabel"><g transform="translate(-74.7437515258789, -10.40000057220459)" class="label"><foreignObject height="20.80000114440918" width="149.4875030517578"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">CONFIG_UPDATE信号</span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(266.66874504089355, 71.70000076293945)" id="flowchart-XML-1294" class="node default configStyle flowchart-label"><rect height="93.4000015258789" width="184.0625" y="-46.70000076293945" x="-92.03125" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-84.53125, -39.20000076293945)" style="" class="label"><rect/><foreignObject height="78.4000015258789" width="169.0625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">metricsToKafka.cfg.xml<br />- direct_kafka_rule<br />- server配置<br />- kafka配置</span></div></foreignObject></g></g><g transform="translate(657.3374996185303, 1568.9000053405762)" id="flowchart-K1-1291" class="node default kafkaStyle flowchart-label"><rect height="33.39999961853027" width="139.1875" y="-16.699999809265137" x="-69.59375" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-62.09375, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="124.1875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Topic: chfp_topic</span></div></foreignObject></g></g><g transform="translate(1230.9374980926514, 1568.9000053405762)" id="flowchart-K2-1292" class="node default kafkaStyle flowchart-label"><rect height="33.39999961853027" width="187.40000915527344" y="-16.699999809265137" x="-93.70000457763672" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-86.20000457763672, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="172.40000915527344"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Topic: hostAlive_topic-1</span></div></foreignObject></g></g><g transform="translate(1555.943754196167, 1568.9000053405762)" id="flowchart-K3-1293" class="node default kafkaStyle flowchart-label"><rect height="33.39999961853027" width="187.40000915527344" y="-16.699999809265137" x="-93.70000457763672" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-86.20000457763672, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="172.40000915527344"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Topic: hostAlive_topic-2</span></div></foreignObject></g></g><g transform="translate(591.8312435150146, 920.3000068664551)" id="flowchart-CP2-1266" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="71.4000015258789" y="-17.90000057220459" x="-35.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-28.200000762939453, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="56.400001525878906"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">子进程2</span></div></foreignObject></g></g><g transform="translate(586.4312438964844, 1166.9000053405762)" id="flowchart-MQ2-1271" class="node default queueStyle flowchart-label"><rect height="35.80000114440918" width="79" y="-17.90000057220459" x="-39.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息队列</span></div></foreignObject></g></g><g transform="translate(586.4312438964844, 1252.7000045776367)" id="flowchart-KQ2-1272" class="node default queueStyle flowchart-label"><rect height="35.80000114440918" width="87.01250457763672" y="-17.90000057220459" x="-43.50625228881836" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-36.00625228881836, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="72.01250457763672"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka队列</span></div></foreignObject></g></g><g transform="translate(487.7312431335449, 1388.5000038146973)" id="flowchart-KW4-1269" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="127.4000015258789" y="-17.90000057220459" x="-63.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56.20000076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka写入线程1</span></div></foreignObject></g></g><g transform="translate(728.4812450408936, 1388.5000038146973)" id="flowchart-KW5-1270" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="127.4000015258789" y="-17.90000057220459" x="-63.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56.20000076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka写入线程2</span></div></foreignObject></g></g><g transform="translate(671.1312446594238, 1031.1000061035156)" id="flowchart-MP4-1267" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="119.4000015258789" y="-17.90000057220459" x="-59.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-52.20000076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="104.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息处理线程1</span></div></foreignObject></g></g><g transform="translate(501.7312431335449, 1031.1000061035156)" id="flowchart-MP5-1268" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="119.4000015258789" y="-17.90000057220459" x="-59.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-52.20000076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="104.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息处理线程2</span></div></foreignObject></g></g><g transform="translate(1356.0406246185303, 920.3000068664551)" id="flowchart-CP1-1231" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="71.4000015258789" y="-17.90000057220459" x="-35.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-28.200000762939453, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="56.400001525878906"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">子进程1</span></div></foreignObject></g></g><g transform="translate(1270.8874988555908, 1166.9000053405762)" id="flowchart-MQ1-1238" class="node default queueStyle flowchart-label"><rect height="35.80000114440918" width="79" y="-17.90000057220459" x="-39.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息队列</span></div></foreignObject></g></g><g transform="translate(1270.8874988555908, 1252.7000045776367)" id="flowchart-KQ1-1239" class="node default queueStyle flowchart-label"><rect height="35.80000114440918" width="87.01250457763672" y="-17.90000057220459" x="-43.50625228881836" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-36.00625228881836, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="72.01250457763672"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka队列</span></div></foreignObject></g></g><g transform="translate(1132.8906211853027, 1388.5000038146973)" id="flowchart-KW1-1235" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="127.4000015258789" y="-17.90000057220459" x="-63.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56.20000076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka写入线程1</span></div></foreignObject></g></g><g transform="translate(1328.2374992370605, 1388.5000038146973)" id="flowchart-KW2-1236" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="127.4000015258789" y="-17.90000057220459" x="-63.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56.20000076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka写入线程2</span></div></foreignObject></g></g><g transform="translate(1555.943754196167, 1388.5000038146973)" id="flowchart-KW3-1237" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="129.2249984741211" y="-17.90000057220459" x="-64.61249923706055" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-57.11249923706055, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="114.2249984741211"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Kafka写入线程N</span></div></foreignObject></g></g><g transform="translate(1440.7406253814697, 1031.1000061035156)" id="flowchart-MP1-1232" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="119.4000015258789" y="-17.90000057220459" x="-59.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-52.20000076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="104.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息处理线程1</span></div></foreignObject></g></g><g transform="translate(1271.3406238555908, 1031.1000061035156)" id="flowchart-MP2-1233" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="119.4000015258789" y="-17.90000057220459" x="-59.70000076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-52.20000076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="104.4000015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息处理线程2</span></div></foreignObject></g></g><g transform="translate(1101.034372329712, 1031.1000061035156)" id="flowchart-MP3-1234" class="node default threadStyle flowchart-label"><rect height="35.80000114440918" width="121.2125015258789" y="-17.90000057220459" x="-60.60625076293945" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-53.10625076293945, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="106.2125015258789"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">消息处理线程N</span></div></foreignObject></g></g><g transform="translate(1060.8437490463257, 407.100004196167)" id="flowchart-SM-1215" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="127" y="-17.90000057220459" x="-63.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-56, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">服务管理器线程</span></div></foreignObject></g></g><g transform="translate(1356.0406246185303, 739.9000053405762)" id="flowchart-P1-1218" class="node default queueStyle flowchart-label"><rect height="33.39999961853027" width="53.775001525878906" y="-16.699999809265137" x="-26.887500762939453" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-19.387500762939453, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="38.775001525878906"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Pipe1</span></div></foreignObject></g></g><g transform="translate(591.8312435150146, 739.9000053405762)" id="flowchart-P2-1219" class="node default queueStyle flowchart-label"><rect height="33.39999961853027" width="53.775001525878906" y="-16.699999809265137" x="-26.887500762939453" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-19.387500762939453, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="38.775001525878906"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Pipe2</span></div></foreignObject></g></g><g transform="translate(1590.2312564849854, 739.9000053405762)" id="flowchart-PN-1220" class="node default queueStyle flowchart-label"><rect height="33.39999961853027" width="55.587501525878906" y="-16.699999809265137" x="-27.793750762939453" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-20.293750762939453, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="40.587501525878906"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">PipeN</span></div></foreignObject></g></g><g transform="translate(996.0031223297119, 574.100004196167)" id="flowchart-T1-1216" class="node default threadStyle flowchart-label"><rect height="56.60000228881836" width="115.17500305175781" y="-28.30000114440918" x="-57.587501525878906" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-50.087501525878906, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="100.17500305175781"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">TCP监听线程1<br />端口8686</span></div></foreignObject></g></g><g transform="translate(817.6312503814697, 574.100004196167)" id="flowchart-T2-1217" class="node default threadStyle flowchart-label"><rect height="56.60000228881836" width="115.17500305175781" y="-28.30000114440918" x="-57.587501525878906" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-50.087501525878906, -20.80000114440918)" style="" class="label"><rect/><foreignObject height="41.60000228881836" width="100.17500305175781"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">TCP监听线程2<br />端口8688</span></div></foreignObject></g></g><g transform="translate(1075.0906248092651, 71.70000076293945)" id="flowchart-MP-1205" class="node default processStyle flowchart-label"><rect height="35.80000114440918" width="63" y="-17.90000057220459" x="-31.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-24, -10.40000057220459)" style="" class="label"><rect/><foreignObject height="20.80000114440918" width="48"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">主进程</span></div></foreignObject></g></g><g transform="translate(867.9874982833862, 241.3000030517578)" id="flowchart-CFG-1206" class="node default configStyle flowchart-label"><rect height="54.20000076293945" width="79.80000305175781" y="-27.100000381469727" x="-39.900001525878906" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32.400001525878906, -19.600000381469727)" style="" class="label"><rect/><foreignObject height="39.20000076293945" width="64.80000305175781"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">配置管理<br />DCConfig</span></div></foreignObject></g></g><g transform="translate(997.3874998092651, 241.3000030517578)" id="flowchart-LOG-1207" class="node default configStyle flowchart-label"><rect height="54.20000076293945" width="79" y="-27.100000381469727" x="-39.5" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-32, -19.600000381469727)" style="" class="label"><rect/><foreignObject height="39.20000076293945" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">日志系统<br />logging2</span></div></foreignObject></g></g><g transform="translate(1152.7937498092651, 241.3000030517578)" id="flowchart-SIG-1208" class="node default default flowchart-label"><rect height="54.20000076293945" width="131.8125" y="-27.100000381469727" x="-65.90625" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-58.40625, -19.600000381469727)" style="" class="label"><rect/><foreignObject height="39.20000076293945" width="116.8125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">信号处理<br />SIGINT/SIGTERM</span></div></foreignObject></g></g><g transform="translate(314.0124969482422, 407.100004196167)" id="flowchart-C1-1202" class="node default default flowchart-label"><rect height="33.39999961853027" width="70.875" y="-16.699999809265137" x="-35.4375" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-27.9375, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="55.875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Client 1</span></div></foreignObject></g></g><g transform="translate(193.1374969482422, 407.100004196167)" id="flowchart-C2-1203" class="node default default flowchart-label"><rect height="33.39999961853027" width="70.875" y="-16.699999809265137" x="-35.4375" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-27.9375, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="55.875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Client 2</span></div></foreignObject></g></g><g transform="translate(71.3499984741211, 407.100004196167)" id="flowchart-C3-1204" class="node default default flowchart-label"><rect height="33.39999961853027" width="72.70000076293945" y="-16.699999809265137" x="-36.35000038146973" ry="0" rx="0" style="" class="basic label-container"/><g transform="translate(-28.850000381469727, -9.199999809265137)" style="" class="label"><rect/><foreignObject height="18.399999618530273" width="57.70000076293945"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Client N</span></div></foreignObject></g></g></g></g></g></svg>