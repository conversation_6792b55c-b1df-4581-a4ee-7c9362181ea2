#!/usr/bin/python
# -*- coding: utf-8 -*-
# Kafka写入器

import threading
import logging2
import queue
import time
import json
import traceback
try:
    from kafka import KafkaProducer
except ImportError:
    logging2.error("未安装kafka-python库，请安装: pip install kafka-python")
    KafkaProducer = None

class MetricsKafkaWriter:
    """Kafka写入器 - 负责将消息写入Kafka"""
    
    def __init__(self, configObj, thread_num, kafkaQueue):
        self.m_configObj = configObj
        self.m_thread_num = thread_num
        self.m_kafkaQueue = kafkaQueue
        self.m_isRun = True
        self.m_threads = []
        self.m_producer = None
        self.m_kafka_config = {}
        self.load_kafka_config()
    
    def load_kafka_config(self):
        """加载Kafka配置"""
        try:
            self.m_kafka_config = {
                'bootstrap_servers': self.m_configObj.get_single_value('kafka', 'bootstrap_servers'),
                'safe_cert': int(self.m_configObj.get_single_value('kafka', 'safe_cert')),
            }
            
            # 如果需要认证
            if self.m_kafka_config['safe_cert'] == 1:
                self.m_kafka_config.update({
                    'security_protocol': self.m_configObj.get_single_value('kafka', 'security_protocol'),
                    'sasl_mechanism': self.m_configObj.get_single_value('kafka', 'sasl_mechanism'),
                    'sasl_plain_username': self.m_configObj.get_single_value('kafka', 'sasl_plain_username'),
                    'sasl_plain_password': self.m_configObj.get_single_value('kafka', 'sasl_plain_password'),
                })
            
            logging2.info("Kafka配置加载完成: %s" % self.m_kafka_config['bootstrap_servers'])
        except Exception as err:
            logging2.error("加载Kafka配置异常: %s" % str(err))
    
    def create_producer(self):
        """创建Kafka生产者"""
        try:
            if not KafkaProducer:
                logging2.error("Kafka生产者类未可用")
                return None
            
            producer_config = {
                'bootstrap_servers': self.m_kafka_config['bootstrap_servers'].split(','),
                'value_serializer': lambda v: str(v).encode('utf-8'),
                'key_serializer': lambda k: str(k).encode('utf-8') if k else None,
                'retries': 3,
                'request_timeout_ms': 30000,
                'retry_backoff_ms': 1000,
            }
            
            # 如果需要认证
            if self.m_kafka_config['safe_cert'] == 1:
                producer_config.update({
                    'security_protocol': self.m_kafka_config['security_protocol'],
                    'sasl_mechanism': self.m_kafka_config['sasl_mechanism'],
                    'sasl_plain_username': self.m_kafka_config['sasl_plain_username'],
                    'sasl_plain_password': self.m_kafka_config['sasl_plain_password'],
                })
            
            producer = KafkaProducer(**producer_config)
            logging2.info("Kafka生产者创建成功")
            return producer
            
        except Exception as err:
            logging2.error("创建Kafka生产者异常: %s" % str(err))
            traceback.print_exc()
            return None
    
    def start(self):
        """启动Kafka写入器"""
        try:
            # 创建Kafka生产者
            self.m_producer = self.create_producer()
            if not self.m_producer:
                logging2.error("Kafka生产者创建失败")
                return False
            
            # 启动写入线程
            for i in range(self.m_thread_num):
                thread = threading.Thread(target=self.write_messages, args=(i,))
                thread.daemon = True
                thread.start()
                self.m_threads.append(thread)
                logging2.debug("Kafka写入线程 %d 启动" % i)
            
            logging2.info("Kafka写入器启动成功，线程数: %d" % self.m_thread_num)
            return True
        except Exception as err:
            logging2.error("启动Kafka写入器异常: %s" % str(err))
            return False
    
    def stop(self):
        """停止Kafka写入器"""
        self.m_isRun = False
        
        # 等待线程结束
        for thread in self.m_threads:
            thread.join(timeout=5)
        
        # 关闭生产者
        if self.m_producer:
            try:
                self.m_producer.flush()
                self.m_producer.close()
                logging2.info("Kafka生产者已关闭")
            except Exception as err:
                logging2.error("关闭Kafka生产者异常: %s" % str(err))
        
        logging2.info("Kafka写入器已停止")
    
    def write_messages(self, thread_id):
        """Kafka写入线程主循环"""
        logging2.debug("Kafka写入线程 %d 开始运行" % thread_id)
        
        while self.m_isRun:
            try:
                # 从队列获取消息，设置超时避免阻塞
                try:
                    kafka_message = self.m_kafkaQueue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 写入Kafka
                self.send_to_kafka(kafka_message)
                
                # 标记任务完成
                self.m_kafkaQueue.task_done()
                
            except Exception as err:
                logging2.error("线程 %d 写入Kafka异常: %s" % (thread_id, str(err)))
                traceback.print_exc()
                time.sleep(0.1)
        
        logging2.debug("Kafka写入线程 %d 退出" % thread_id)
    
    def send_to_kafka(self, kafka_message):
        """发送消息到Kafka"""
        try:
            topic = kafka_message['topic']
            data = kafka_message['data']
            model = kafka_message['model']
            client_addr = kafka_message['client_addr']
            timestamp = kafka_message['timestamp']
            
            if not self.m_producer:
                logging2.error("Kafka生产者未初始化")
                return
            
            # 发送消息到Kafka
            future = self.m_producer.send(topic, value=data, key=model)
            
            # 异步回调处理
            def on_send_success(record_metadata):
                logging2.debug("消息发送成功: topic=%s, partition=%d, offset=%d, model=%s" % 
                             (record_metadata.topic, record_metadata.partition, 
                              record_metadata.offset, model))
            
            def on_send_error(exception):
                logging2.error("消息发送失败: topic=%s, model=%s, error=%s" % 
                             (topic, model, str(exception)))
            
            future.add_callback(on_send_success)
            future.add_errback(on_send_error)
            
        except Exception as err:
            logging2.error("发送Kafka消息异常: %s" % str(err))
            traceback.print_exc()
    
    def flush(self):
        """刷新生产者缓冲区"""
        try:
            if self.m_producer:
                self.m_producer.flush()
        except Exception as err:
            logging2.error("刷新Kafka生产者异常: %s" % str(err))