#!/usr/bin/python
# -*- coding: utf-8 -*-
#定时从数据库加载数据

import queue
import threading
import logging2
import time
import traceback
from DBPyMgr import DBPyMgr

class DCKpiDbLoad(threading.Thread):
    def __init__(self, isRun, cfgObj ):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_cfgObj = cfgObj
        self.m_dataDictMaster = {}
        self.m_dataDictSlave = {}   
        self.m_dataDictFlag = "slave"
        self.m_comDataListMaster = []
        self.m_comDataListSlave = []
        self.m_loadTime = 0   
        value = self.m_cfgObj.get_single_value('common', 'comModule') 
        self.m_kpiComName = 'common' if value == None else value
        self.m_db_mgr = DBPyMgr(self.m_cfgObj.get_single_value('common', 'sqlConfig'))
        if not self.m_db_mgr.init():
            logging2.error("db init failed")
            raise Exception("db init failed")

        self.loadData()        
        self.m_loadTime = time.time()
        pass

    def setRunState(self, isRun):
        self.m_isRun = isRun

    def getModuleData(self, moduleName):
        try:
            if self.m_dataDictFlag == "master":
                return self.m_dataDictMaster.get(moduleName, None) 
            else:
                return self.m_dataDictSlave.get(moduleName, None) 
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return None

    def getCommonData(self):
        try:
            if self.m_dataDictFlag == "master":
                return self.m_comDataListMaster
            else:
                return self.m_comDataListSlave
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return None

    def loadDataFromDB(self):
        try:
            result = self.m_db_mgr.excute_sql('getDetailDef', None, 2)
            logging2.debug("db rows:%d"%(len(result)))
            dataDict = {}
            comDataList = []
            #`module`,kpi_name,kpi_code,kpi_type
            for row in result:
                rowList = list()
                for field in row:
                    if field != None and isinstance(field, str) == True:
                        field = field.strip()
                    rowList.append(field)
                if rowList[0] == self.m_kpiComName:
                    comDataList.append((row[1]).lower())
                else:
                    if rowList[0].lower() not in dataDict:
                        dataDict[rowList[0].lower()] = {}
                    dataDict[rowList[0].lower()][rowList[1].lower()] = (rowList[2], rowList[3])
            # logging2.debug("comDataList:%s, dataDict:%s"%(comDataList, dataDict))
            return dataDict, comDataList
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return {},[]

    def loadData(self):
        try:
            if self.m_dataDictFlag == "master":
                self.m_dataDictSlave,self.m_comDataListSlave = self.loadDataFromDB()
                if len(self.m_dataDictSlave) == 0 or len(self.m_comDataListSlave) == 0:
                    logging2.warn("load data fail for slave, still use the master!")
                else:
                    self.m_dataDictFlag = "slave"
                # logging2.debug("slave dataDict:%s, comData:%s"%(self.m_dataDictMaster, self.m_comDataListMaster))
            else:
                self.m_dataDictMaster,self.m_comDataListMaster = self.loadDataFromDB()
                if len(self.m_dataDictMaster) == 0 or len(self.m_comDataListMaster) == 0:
                    logging2.warn("load data fail for master, still use the slave!")
                else:
                    self.m_dataDictFlag = "master"
                # logging2.debug("master dataDict:%s, comData:%s"%(self.m_dataDictMaster, self.m_comDataListMaster))
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
    
    def run(self):
        while True:
            if not self.m_isRun:
                logging2.warn("DCKpiDbLoad thread exit")
                break
            try:
                now_time = time.time()
                if now_time - self.m_loadTime > 3600:#1h加载一次
                    self.loadData()
                    self.m_loadTime = now_time

                #增加日志级别修改检测功能，避免在子进程中重新增加日志级别修改的线程
                if self.m_cfgObj.update_config():
                    cfg_value = self.m_cfgObj.get_single_value('log', 'loglevel')
                    if cfg_value != None:
                        logging2.reSetLogLevel(cfg_value)
                        logging2.warn("reSetLogLevel[%s]" % cfg_value)

                time.sleep(10)
            except Exception as err:
                stack_trace = traceback.format_exc()
                logging2.error(stack_trace)
                traceback.print_exc()
                logging2.error(str(err))
        self.m_db_mgr.stop()