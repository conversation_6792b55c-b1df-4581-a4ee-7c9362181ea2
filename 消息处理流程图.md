sequenceDiagram
    participant Client as 客户端
    participant TCP as TCP监听线程
    participant SM as 服务管理器
    participant CP as 子进程
    participant MP as 消息处理器
    participant K<PERSON> as Kafka写入器
    participant Ka<PERSON><PERSON> as <PERSON><PERSON><PERSON>集群

    Note over Client,Kafka: 消息处理完整流程

    %% 1. 连接建立
    Client->>TCP: TCP连接请求
    TCP->>TCP: accept()接受连接
    TCP->>SM: 通过管道发送连接
    SM->>CP: 负载均衡分发连接

    %% 2. 消息接收
    Client->>CP: 发送JSON消息<br/>{"model":"chfp","body":"data"}
    CP->>CP: 验证JSON格式
    CP->>CP: 检查model和body字段

    %% 3. 消息队列处理
    CP->>MP: 放入消息队列
    Note over MP: 消息处理线程池<br/>处理队列中的消息

    %% 4. 规则匹配
    MP->>MP: 根据model查找<br/>direct_kafka_rule规则
    MP->>MP: model="chfp"<br/>→ topic="chfp_topic"

    %% 5. <PERSON>fka队列
    MP->>KW: 放入Kafka队列<br/>{topic,data,model,timestamp}

    %% 6. Kafka写入
    Note over KW: Kafka写入线程池<br/>处理队列中的消息
    KW->>Kafka: 写入到指定topic<br/>key=model, value=body

    %% 7. 响应确认
    Kafka-->>KW: 写入确认
    KW-->>KW: 记录成功日志

    %% 配置热更新流程
    Note over Client,Kafka: 配置热更新流程

    Note over SM: 主进程检测配置文件变更
    SM->>CP: 发送CONFIG_UPDATE信号
    CP->>CP: 重新加载配置文件
    CP->>MP: 通知重新加载规则
    MP->>MP: 清空并重新加载<br/>direct_kafka_rule规则
    Note over MP: 新规则立即生效