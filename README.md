# MetricsToKafka 指标接收写Kafka程序

## 概述

MetricsToKafka是一个简化版的指标接收写Kafka程序，基于现有的kpiAggregation架构设计。它支持TCP监听、多进程、多线程处理，能够接收JSON格式的指标数据并根据配置规则写入对应的Kafka主题。

## 特性

- **多进程多线程架构**: 支持多个子进程处理连接，每个子进程内部使用线程池处理消息
- **TCP监听**: 支持多个IP端口监听，每个端口独立处理
- **异步连接处理**: 使用asyncio处理客户端连接，提高并发性能
- **简化消息处理**: 根据JSON消息的model字段匹配规则，将body内容写入对应的Kafka主题
- **配置热更新**: 支持配置文件动态重新加载（除需要重启的配置项外）
- **日志系统**: 完整的日志记录和管理功能
- **加解密支持**: 集成AES加解密功能

## 架构设计

```
主进程 (metricsToKafka.py)
├── 配置管理 (DCConfig)
├── 日志系统 (logging2)
└── 服务管理器 (MetricsServerManager)
    ├── TCP监听线程 (MetricsTcpService) × N个端口
    └── 子进程 (MetricsConnectionManager) × N个
        ├── 连接处理 (异步)
        ├── 消息处理线程池 (MetricsMessageProcessor)
        └── Kafka写入线程池 (MetricsKafkaWriter)
```

## 消息格式

程序接收JSON格式的消息：

```json
{
    "model": "chfp",
    "body": "11|22|33|xx"
}
```

- `model`: 对应配置文件中的rule_name
- `body`: 实际要写入Kafka的数据内容

## 配置文件

配置文件位于 `cfg/metricsToKafka.cfg.xml`，主要配置项包括：

### 1. direct_kafka_rule配置

```xml
<direct_kafka_rule>
    <group1>
        <param name='rule_name'>openapi-1</param>
        <param name='kafka_topic'>hostAlive_topic-1</param>
    </group1>
    <group2>
        <param name='rule_name'>chfp</param>
        <param name='kafka_topic'>chfp_topic</param>
    </group2>
</direct_kafka_rule>
```

### 2. 服务器配置

```xml
<server>
    <group1>
        <param name="server_ip">**************</param>
        <param name="server_port">8686</param>
    </group1>
</server>
```

### 3. Kafka配置

```xml
<kafka>
    <param name="safe_cert">0</param>
    <param name="bootstrap_servers">**************:9092</param>
    <param name="security_protocol">SASL_PLAINTEXT</param>
    <param name="sasl_mechanism">PLAIN</param>
    <param name="sasl_plain_username">itete</param>
    <param name="sasl_plain_password">xH9%iO2*tK23aR9f</param>
</kafka>
```

## 安装和使用

### 1. 环境准备

```bash
# 安装Python依赖
pip3 install kafka-python pycrypto

# 或者运行安装脚本
./make.sh
```

### 2. 启动程序

```bash
cd src
python3 metricsToKafka.py ../cfg/metricsToKafka.cfg.xml
```

### 3. 停止程序

```bash
# 优雅停止
kill -TERM <pid>

# 或者
kill -INT <pid>  # Ctrl+C
```

### 4. 测试连接

可以使用telnet或自定义客户端测试：

```bash
# 连接测试
telnet ************** 8686

# 发送测试消息
{"model": "chfp", "body": "test_data_123"}
```

### 5. 功能测试

项目提供了测试脚本：

```bash
# 基本功能测试
python3 test_simple.py

# 配置热更新测试
python3 test_config_update.py
```

## 日志管理

- 日志文件位置: `./log/`
- 日志文件命名: `log_metricsToKafka_<pid>.log` (主进程)
- 子进程日志: `log_metricsC_<pid>.log`
- 支持动态调整日志级别: DEBUG, INFO, WARNING, ERROR

## 监控和管理

### 进程监控

程序会自动监控子进程状态，如果子进程异常退出会自动重启。如果1分钟内重启超过3次，主进程会退出。

### 配置热更新

程序支持配置文件的热更新机制，主程序每10秒检查一次配置文件是否更新。

支持以下配置的热更新（无需重启程序）：
- 日志级别 (loglevel) - 实时生效
- direct_kafka_rule规则 - 自动通知所有子进程重新加载规则

热更新流程：
1. 主进程检测到配置文件更新
2. 重新加载配置到内存
3. 通过进程间管道通知所有子进程
4. 子进程重新加载direct_kafka_rule规则
5. 新规则立即生效，处理后续消息

需要重启程序的配置：
- 服务器IP端口
- Kafka连接配置
- 线程数配置

## 目录结构

```
metricsToKafka/
├── make.sh                          # 安装脚本
├── README.md                        # 说明文档
├── test_simple.py                   # 基本功能测试脚本
├── test_config_update.py            # 配置热更新测试脚本
├── cfg/                             # 配置文件目录
│   └── metricsToKafka.cfg.xml
├── src/                             # 源代码目录
│   ├── __init__.py
│   ├── metricsToKafka.py            # 主程序入口
│   ├── MetricsServerManager.py      # 服务管理器
│   ├── MetricsConnectionManager.py  # 连接管理器
│   ├── MetricsMessageProcessor.py   # 消息处理器
│   ├── MetricsKafkaWriter.py        # Kafka写入器
│   ├── DCConfig.py                  # 配置管理
│   ├── logging2.py                  # 日志系统
│   └── desTool.py                   # 加解密工具
└── log/                             # 日志目录
```

## 性能参数

可调整的性能参数：

- `workPidNums`: 子进程数量，建议设置为CPU核心数
- `recv_thread_num`: 消息接收线程池大小
- `kafka_thread_num`: Kafka写入线程池大小  
- `queue_size`: 内部队列大小

## 故障排除

### 常见问题

1. **端口绑定失败**
   - 检查端口是否被占用
   - 检查IP地址配置是否正确

2. **Kafka连接失败**
   - 检查Kafka服务器是否可达
   - 验证认证配置是否正确

3. **消息处理失败**
   - 检查JSON格式是否正确
   - 确认model字段对应的规则是否存在

### 日志分析

- 查看主进程日志了解整体状态
- 查看子进程日志了解具体的消息处理情况
- 注意ERROR和WARNING级别的日志

## 与原版差异

与kpiAggregation相比的主要简化：

1. **消息格式简化**: 只处理包含model和body的简单JSON格式
2. **处理逻辑简化**: 取消复杂的消息合并、转换逻辑，直接根据规则转发
3. **配置简化**: 使用direct_kafka_rule替代复杂的mes_rule配置
4. **去除数据库依赖**: 不需要数据库相关的配置和处理

## 扩展性

程序设计考虑了扩展性：

- 可以轻松添加新的消息处理逻辑
- 支持配置不同的消息格式验证规则
- 可以集成更多的数据源和目标